"""
Test AccountGenerator - Ki<PERSON><PERSON> tra AccountGenerator đã hoạt động chưa
"""

import sys
from pathlib import Path

# Thêm src vào path
sys.path.append(str(Path(__file__).parent / "src"))

def test_account_generator():
    """Test AccountGenerator"""
    print("🧪 Test AccountGenerator")
    print("=" * 50)
    
    try:
        from account_generator import AccountGenerator
        print("✅ Import AccountGenerator thành công")
        
        # Tạo generator
        generator = AccountGenerator()
        print("✅ Khởi tạo AccountGenerator thành công")
        
        # Test generate_account method
        print("\n🔄 Test generate_account()...")
        account = generator.generate_account()
        print("✅ generate_account() hoạt động thành công")
        
        # Hiển thị kết quả
        print("\n📋 Thông tin tài khoản được tạo:")
        print("-" * 40)
        for key, value in account.items():
            print(f"{key:15}: {value}")
        
        # Test các method riêng lẻ
        print("\n🔄 Test các method riêng lẻ...")
        
        username = generator.generate_username()
        print(f"✅ Username: {username}")
        
        password = generator.generate_password()
        print(f"✅ Password: {password}")
        
        email = generator.generate_email()
        print(f"✅ Email: {email}")
        
        phone = generator.generate_phone()
        print(f"✅ Phone: {phone}")
        
        name_info = generator.generate_name()
        print(f"✅ Name: {name_info['full_name']}")
        
        print("\n🎉 Tất cả test đều THÀNH CÔNG!")
        return True
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        return False
    except AttributeError as e:
        print(f"❌ Lỗi method: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi khác: {e}")
        return False

def test_real_registration_bot():
    """Test RealRegistrationBot"""
    print("\n🧪 Test RealRegistrationBot")
    print("=" * 50)
    
    try:
        from real_registration_bot import RealRegistrationBot
        print("✅ Import RealRegistrationBot thành công")
        
        # Tạo bot (không khởi tạo driver)
        def test_callback(message, level):
            print(f"[{level}] {message}")
        
        bot = RealRegistrationBot(ui_callback=test_callback)
        print("✅ Khởi tạo RealRegistrationBot thành công")
        
        # Test account generator
        if hasattr(bot, 'account_generator'):
            print("✅ Bot có account_generator")
            
            if hasattr(bot.account_generator, 'generate_account'):
                print("✅ account_generator có method generate_account")
                
                # Test tạo account
                account = bot.account_generator.generate_account()
                print(f"✅ Tạo account thành công: {account['username']}")
            else:
                print("❌ account_generator THIẾU method generate_account")
                return False
        else:
            print("❌ Bot THIẾU account_generator")
            return False
        
        print("\n🎉 RealRegistrationBot test THÀNH CÔNG!")
        return True
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi khác: {e}")
        return False

def main():
    """Hàm main"""
    print("🚀 Test Tool Components")
    print("=" * 60)
    
    # Test AccountGenerator
    success1 = test_account_generator()
    
    # Test RealRegistrationBot
    success2 = test_real_registration_bot()
    
    # Kết quả tổng
    print("\n" + "=" * 60)
    print("📊 KẾT QUẢ TỔNG:")
    print(f"AccountGenerator: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"RealRegistrationBot: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 TẤT CẢ TEST THÀNH CÔNG! Tool sẵn sàng hoạt động.")
    else:
        print("\n❌ CÓ LỖI! Cần sửa trước khi sử dụng.")
    
    print("=" * 60)
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
