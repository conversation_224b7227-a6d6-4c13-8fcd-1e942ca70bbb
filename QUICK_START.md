# 🚀 Quick Start Guide

## ✅ Đã hoàn thành:

### 🗂️ Cấu trúc mới:
- ✅ X<PERSON>a tất cả file C# không cần thiết
- ✅ Tổ chức lại folder theo cấu trúc tối ưu
- ✅ Di chuyển dữ liệu vào `data/` folders
- ✅ Tạo giao diện UI hoàn chỉnh

### 🖥️ Giao diện UI:
- ✅ **Full UI** (`ui/main_window.py`): Giao diện đầy đủ với 4 tabs
- ✅ **Simple UI** (`ui/simple_ui.py`): Giao diện đơn giản để test
- ✅ **Launcher** (`launcher.py`): Chọn chế độ khởi động

## 🎯 Cách sử dụng:

### 1. Simple UI (Khuyến nghị - Ổn định):
```bash
# Cách 1: Double-click
start_simple.bat

# Cách 2: Command line
python simple_launcher.py

# Cách 3: Test trực tiếp
python test_simple_ui.py
```

### 2. Console Launcher:
```bash
# Command line với menu
python console_launcher.py
```

### 3. Launcher gốc (có thể có lỗi):
```bash
# Nếu muốn thử launcher gốc
python launcher.py
```

### 4. Chọn chế độ trong Simple Launcher:
- **🖥️ Simple UI**: Giao diện đơn giản, ổn định
- **🧪 Test UI**: Kiểm tra UI hoạt động
- **💻 Console**: Menu console
- **❌ Thoát**: Đóng launcher

## 🔧 Troubleshooting:

### Nếu gặp lỗi import:
1. Chạy Simple UI trước: `python test_simple_ui.py`
2. Kiểm tra các file trong `src/` folder
3. Cài đặt dependencies: `pip install -r requirements.txt`

### Nếu UI không hiển thị:
1. Đảm bảo đã cài tkinter (có sẵn trong Python)
2. Thử chế độ Console thay thế

## 📁 Files quan trọng:

### Khởi động (Ổn định):
- `simple_launcher.py` - **Launcher chính (Khuyến nghị)**
- `start_simple.bat` - **Batch file khởi động**
- `test_simple_ui.py` - Test Simple UI trực tiếp
- `console_launcher.py` - Console menu

### Khởi động (Có thể có lỗi):
- `launcher.py` - Launcher gốc
- `start_ui.bat` - Batch file gốc

### UI:
- `ui/main_window.py` - Giao diện đầy đủ
- `ui/simple_ui.py` - Giao diện đơn giản

### Source:
- `src/main.py` - Tool console gốc
- `src/config.py` - Cấu hình (đã cập nhật)
- `src/proxy_manager.py` - Quản lý proxy
- `src/registration_bot.py` - Bot đăng ký

### Data:
- `data/accounts/` - File tài khoản
- `data/logs/` - File log
- `data/screenshots/` - Ảnh chụp màn hình
- `data/proxies/` - File proxy

## 🎉 Kết quả:

✅ **Đã chuyển đổi hoàn toàn** từ tool command-line sang **giao diện UI**
✅ **Cấu trúc folder** được tối ưu và dễ quản lý
✅ **Dual mode**: UI và Console
✅ **Xóa sạch** tất cả code C# không cần thiết
✅ **Tích hợp đầy đủ** với logic hiện có

---

**🚀 Bắt đầu với Simple UI để test trước!**
