"""
Chrome Auto Registration Tool - Launcher
File khởi động chính cho tool với giao diện UI
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def setup_environment():
    """Thiết lập môi trường và đường dẫn"""
    # Thêm các thư mục vào Python path
    current_dir = Path(__file__).parent
    src_dir = current_dir / "src"
    ui_dir = current_dir / "ui"

    for path in [str(src_dir), str(ui_dir)]:
        if path not in sys.path:
            sys.path.insert(0, path)

    # Thiết lập encoding cho Windows
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def check_dependencies():
    """Kiểm tra các dependencies cần thiết"""
    required_packages = [
        'selenium',
        'requests',
        'fake_useragent',
        'faker',
        'beautifulsoup4',
        'colorama'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        root = tk.Tk()
        root.withdraw()  # Ẩn cửa sổ chính

        message = f"""Thiếu các package sau:
{', '.join(missing_packages)}

Bạn có muốn cài đặt tự động không?
(Sẽ chạy: pip install -r requirements.txt)"""

        result = messagebox.askyesno("Thiếu Dependencies", message)
        root.destroy()

        if result:
            import subprocess
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                             check=True)
                messagebox.showinfo("Thành công", "Đã cài đặt thành công! Vui lòng khởi động lại.")
                return False
            except subprocess.CalledProcessError:
                messagebox.showerror("Lỗi", "Không thể cài đặt dependencies. Vui lòng cài đặt thủ công.")
                return False
        else:
            return False

    return True

def check_file_structure():
    """Kiểm tra cấu trúc file"""
    current_dir = Path(__file__).parent
    required_dirs = ["src", "ui", "data", "temp"]
    required_files = [
        "src/config.py",
        "src/proxy_manager.py",
        "src/registration_bot.py",
        "src/account_generator.py",
        "ui/main_window.py"
    ]

    missing_items = []

    # Kiểm tra thư mục
    for dir_name in required_dirs:
        if not (current_dir / dir_name).exists():
            missing_items.append(f"Thư mục: {dir_name}")

    # Kiểm tra file
    for file_path in required_files:
        if not (current_dir / file_path).exists():
            missing_items.append(f"File: {file_path}")

    if missing_items:
        root = tk.Tk()
        root.withdraw()

        message = f"""Thiếu các file/thư mục sau:
{chr(10).join(missing_items)}

Vui lòng đảm bảo cấu trúc file đúng!"""

        messagebox.showerror("Lỗi cấu trúc", message)
        root.destroy()
        return False

    return True

def show_startup_options():
    """Hiển thị tùy chọn khởi động"""
    root = tk.Tk()
    root.title("Chrome Auto Registration Tool")
    root.geometry("400x300")
    root.resizable(False, False)

    # Center window
    root.eval('tk::PlaceWindow . center')

    # Header
    header_frame = tk.Frame(root, bg='#2E86AB', height=60)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)

    title_label = tk.Label(header_frame, text="🚀 Chrome Auto Registration Tool",
                          font=('Arial', 14, 'bold'), fg='white', bg='#2E86AB')
    title_label.pack(expand=True)

    subtitle_label = tk.Label(header_frame, text="v2.0 - EderGhostVN",
                             font=('Arial', 8), fg='white', bg='#2E86AB')
    subtitle_label.pack()

    # Main content
    content_frame = tk.Frame(root, padx=20, pady=20)
    content_frame.pack(fill='both', expand=True)

    info_label = tk.Label(content_frame,
                         text="Chọn chế độ khởi động:",
                         font=('Arial', 12, 'bold'))
    info_label.pack(pady=10)

    # Buttons
    button_frame = tk.Frame(content_frame)
    button_frame.pack(expand=True)

    def start_ui():
        root.destroy()
        launch_ui_mode()

    def start_console():
        root.destroy()
        launch_console_mode()

    def start_simple_ui():
        root.destroy()
        launch_simple_ui()

    ui_btn = tk.Button(button_frame, text="🖥️ Giao diện UI (Full)",
                      command=start_ui, width=20, height=2,
                      font=('Arial', 10), bg='#28A745', fg='white')
    ui_btn.pack(pady=5)

    simple_ui_btn = tk.Button(button_frame, text="🖥️ Giao diện UI (Simple)",
                             command=start_simple_ui, width=20, height=2,
                             font=('Arial', 10), bg='#FFC107', fg='black')
    simple_ui_btn.pack(pady=5)

    console_btn = tk.Button(button_frame, text="💻 Console",
                           command=start_console, width=20, height=2,
                           font=('Arial', 10), bg='#17A2B8', fg='white')
    console_btn.pack(pady=5)

    # Info
    info_text = """
UI Full: Giao diện đầy đủ với tất cả tính năng
UI Simple: Giao diện đơn giản để test
Console: Chạy trong terminal (như phiên bản cũ)
    """

    info_label2 = tk.Label(content_frame, text=info_text,
                          font=('Arial', 8), fg='gray')
    info_label2.pack(pady=10)

    root.mainloop()

def launch_ui_mode():
    """Khởi động chế độ UI"""
    try:
        from main_window import MainWindow
        app = MainWindow()
        app.run()
    except ImportError as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Lỗi Import", f"Không thể import UI module: {e}")
        root.destroy()
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Lỗi", f"Lỗi khi khởi động UI: {e}")
        root.destroy()

def launch_simple_ui():
    """Khởi động chế độ Simple UI"""
    try:
        from simple_ui import SimpleUI
        app = SimpleUI()
        app.run()
    except ImportError as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Lỗi Import", f"Không thể import Simple UI module: {e}")
        root.destroy()
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Lỗi", f"Lỗi khi khởi động Simple UI: {e}")
        root.destroy()

def launch_console_mode():
    """Khởi động chế độ console"""
    try:
        # Import và chạy main.py từ src
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        import main

        # Chạy tool console
        tool = main.AutoRegistrationTool()
        tool.run()

    except ImportError as e:
        print(f"Lỗi import: {e}")
        print("Vui lòng đảm bảo file main.py đã được di chuyển vào folder src/")
        input("Nhấn Enter để thoát...")
    except Exception as e:
        print(f"Lỗi: {e}")
        input("Nhấn Enter để thoát...")

def main():
    """Hàm main"""
    print("🚀 Chrome Auto Registration Tool - Starting...")

    # Thiết lập môi trường
    setup_environment()

    # Kiểm tra dependencies
    if not check_dependencies():
        return

    # Kiểm tra cấu trúc file
    if not check_file_structure():
        return

    # Hiển thị tùy chọn khởi động
    show_startup_options()

if __name__ == "__main__":
    main()
