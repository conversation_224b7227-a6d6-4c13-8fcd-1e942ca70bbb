"""
Fix Chrome 137 - Đặc biệt cho Chrome version 137.x
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path
import subprocess

def print_step(message):
    """In thông báo với format đẹp"""
    print(f"🔧 {message}")

def clean_chrome_cache():
    """Xóa cache Chrome"""
    try:
        print_step("Đang xóa cache cũ...")
        
        # Xóa cache webdriver-manager
        cache_dirs = [
            Path.home() / ".wdm",
            Path(os.environ.get('TEMP', '')) / ".wdm",
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                    print_step(f"✅ Đã xóa cache: {cache_dir}")
                except:
                    pass
        
        # Xóa chromedriver cũ
        drivers_dir = Path(__file__).parent / "drivers"
        if drivers_dir.exists():
            for item in drivers_dir.iterdir():
                if "chromedriver" in item.name:
                    try:
                        if item.is_file():
                            item.unlink()
                        elif item.is_dir():
                            shutil.rmtree(item)
                        print_step(f"✅ Đã xóa: {item}")
                    except:
                        pass
        
    except Exception as e:
        print_step(f"⚠️ Lỗi xóa cache: {e}")

def download_chromedriver_137():
    """Tải ChromeDriver cho Chrome 137"""
    try:
        print_step("Đang tải ChromeDriver cho Chrome 137...")
        
        # URLs cho Chrome 137 - thử nhiều version
        chrome_137_urls = [
            "https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.56/win64/chromedriver-win64.zip",
            "https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.0/win64/chromedriver-win64.zip",
            "https://storage.googleapis.com/chrome-for-testing-public/*********/win64/chromedriver-win64.zip",
            # Fallback về version 136 nếu 137 chưa có
            "https://storage.googleapis.com/chrome-for-testing-public/136.0.6776.0/win64/chromedriver-win64.zip",
            "https://storage.googleapis.com/chrome-for-testing-public/135.0.6761.0/win64/chromedriver-win64.zip"
        ]
        
        drivers_dir = Path(__file__).parent / "drivers"
        drivers_dir.mkdir(exist_ok=True)
        
        for i, url in enumerate(chrome_137_urls):
            try:
                version = url.split('/')[-3]
                print_step(f"Thử tải ChromeDriver {version}... ({i+1}/{len(chrome_137_urls)})")
                
                response = requests.get(url, timeout=60)
                if response.status_code == 200:
                    zip_path = drivers_dir / "chromedriver.zip"
                    
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    print_step("Đang giải nén...")
                    
                    # Giải nén
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(drivers_dir)
                    
                    # Tìm và di chuyển chromedriver.exe
                    chromedriver_found = False
                    for root, dirs, files in os.walk(drivers_dir):
                        for file in files:
                            if file == "chromedriver.exe":
                                source = Path(root) / file
                                target = drivers_dir / "chromedriver.exe"
                                
                                if target.exists():
                                    target.unlink()
                                
                                shutil.move(str(source), str(target))
                                chromedriver_found = True
                                break
                        if chromedriver_found:
                            break
                    
                    # Dọn dẹp
                    zip_path.unlink()
                    for item in drivers_dir.iterdir():
                        if item.is_dir() and "chromedriver" in item.name:
                            shutil.rmtree(item)
                    
                    if chromedriver_found:
                        print_step(f"✅ Tải thành công ChromeDriver {version}")
                        return True
                
            except Exception as e:
                print_step(f"⚠️ Lỗi tải từ {version}: {str(e)[:50]}...")
                continue
        
        print_step("❌ Không thể tải ChromeDriver cho Chrome 137!")
        print_step("💡 Chrome 137 quá mới, ChromeDriver có thể chưa có")
        return False
        
    except Exception as e:
        print_step(f"❌ Lỗi tải ChromeDriver: {e}")
        return False

def test_chromedriver():
    """Test ChromeDriver"""
    try:
        print_step("Đang test ChromeDriver...")
        
        chromedriver_path = Path(__file__).parent / "drivers" / "chromedriver.exe"
        
        if not chromedriver_path.exists():
            print_step("❌ Không tìm thấy chromedriver.exe")
            return False
        
        result = subprocess.run([str(chromedriver_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print_step(f"✅ ChromeDriver hoạt động: {version}")
            return True
        else:
            print_step(f"❌ ChromeDriver lỗi: {result.stderr}")
            return False
            
    except Exception as e:
        print_step(f"❌ Lỗi test: {e}")
        return False

def create_compatibility_note():
    """Tạo ghi chú về compatibility"""
    try:
        note_file = Path(__file__).parent / "CHROME_137_NOTE.txt"
        with open(note_file, 'w', encoding='utf-8') as f:
            f.write("""
CHROME 137 COMPATIBILITY NOTE
============================

Chrome Version: 137.0.7151.56
Date: """ + str(Path(__file__).stat().st_mtime) + """

Chrome 137 là version rất mới, ChromeDriver có thể chưa hoàn toàn tương thích.

Nếu gặp lỗi:
1. Sử dụng Demo Mode trong Simple UI
2. Downgrade Chrome về version 136 hoặc 135
3. Chờ ChromeDriver update

Demo Mode vẫn hiển thị đầy đủ thông tin tài khoản!
""")
        print_step(f"📝 Đã tạo ghi chú: {note_file}")
    except:
        pass

def main():
    """Hàm main cho Chrome 137"""
    print("=" * 60)
    print("🔧 Fix Chrome 137 - Đặc biệt cho Chrome version 137.x")
    print("=" * 60)
    
    try:
        print_step("Phát hiện Chrome 137.0.7151.56")
        print_step("⚠️ Đây là version rất mới, ChromeDriver có thể chưa tương thích")
        
        # Bước 1: Xóa cache cũ
        clean_chrome_cache()
        
        # Bước 2: Tải ChromeDriver
        if download_chromedriver_137():
            # Bước 3: Test
            if test_chromedriver():
                print_step("🎉 Hoàn thành! ChromeDriver đã sẵn sàng")
                print_step("Có thể chạy tool bình thường")
                create_compatibility_note()
                return True
            else:
                print_step("❌ ChromeDriver không hoạt động với Chrome 137")
        
        # Nếu không thành công
        print_step("💡 Khuyến nghị cho Chrome 137:")
        print_step("1. Sử dụng Demo Mode trong Simple UI")
        print_step("2. Demo Mode vẫn hiển thị đầy đủ thông tin")
        print_step("3. Hoặc downgrade Chrome về version 136")
        
        create_compatibility_note()
        return False
            
    except Exception as e:
        print_step(f"❌ Lỗi chung: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ ChromeDriver đã sẵn sàng cho Chrome 137")
            print("Bây giờ có thể chạy:")
            print("- start_simple.bat")
            print("- python simple_launcher.py")
        else:
            print("⚠️ ChromeDriver chưa tương thích với Chrome 137")
            print("💡 Giải pháp:")
            print("1. Chạy Demo Mode:")
            print("   start_simple.bat → Tick 'Chế độ nhanh' → Bắt đầu")
            print("2. Demo Mode vẫn hiển thị username, password, bank name")
            print("3. Hoặc downgrade Chrome về version 136")
        print("=" * 60)
        
        input("\nNhấn Enter để thoát...")
        
    except KeyboardInterrupt:
        print("\n👋 Đã hủy bởi người dùng")
    except Exception as e:
        print(f"\n❌ Lỗi không mong đợi: {e}")
        input("Nhấn Enter để thoát...")
