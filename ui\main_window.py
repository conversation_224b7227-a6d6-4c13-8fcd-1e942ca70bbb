"""
Chrome Auto Registration Tool - Main UI Window
Giao diện chính cho tool tự động đăng ký tài khoản
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import sys
import os
import time
import datetime
from pathlib import Path

# Thêm src vào path để import các module
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from config import REGISTRATION_CONFIG, OUTPUT_CONFIG, BROWSER_CONFIG
    from proxy_manager import ProxyManager
    from registration_bot import RegistrationBot
    from account_generator import AccountGenerator
except ImportError as e:
    print(f"Lỗi import: {e}")
    print("Vui lòng đảm bảo các file Python đã được di chuyển vào folder src/")
    messagebox.showerror("Lỗi Import", f"Không thể import modules: {e}")
    sys.exit(1)

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Chrome Auto Registration Tool - EderGhostVN")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # Thiết lập icon và style
        self.setup_style()

        # Khởi tạo các manager
        self.proxy_manager = ProxyManager()
        self.account_generator = AccountGenerator()

        # Biến trạng thái
        self.is_running = False
        self.registration_thread = None

        # Tạo giao diện
        self.create_widgets()

        # Cập nhật thông tin ban đầu
        self.update_status()

    def setup_style(self):
        """Thiết lập style cho ứng dụng"""
        style = ttk.Style()
        style.theme_use('clam')

        # Cấu hình màu sắc
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'), foreground='#2E86AB')
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='#28A745')
        style.configure('Error.TLabel', foreground='#DC3545')
        style.configure('Warning.TLabel', foreground='#FFC107')

    def create_widgets(self):
        """Tạo các widget chính"""
        # Header
        self.create_header()

        # Notebook (tabs)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # Tạo các tab
        self.create_registration_tab()
        self.create_proxy_tab()
        self.create_settings_tab()
        self.create_results_tab()

        # Status bar
        self.create_status_bar()

    def create_header(self):
        """Tạo header với logo và thông tin"""
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', padx=10, pady=5)

        # Title
        title_label = ttk.Label(header_frame, text="🚀 Chrome Auto Registration Tool",
                               style='Title.TLabel')
        title_label.pack(side='left')

        # Version info
        version_label = ttk.Label(header_frame, text="v2.0 - EderGhostVN",
                                 font=('Arial', 8))
        version_label.pack(side='right')

    def create_registration_tab(self):
        """Tab đăng ký tài khoản"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📝 Đăng ký tài khoản")

        # Main container với scroll
        canvas = tk.Canvas(tab_frame)
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Cấu hình đăng ký
        config_frame = ttk.LabelFrame(scrollable_frame, text="Cấu hình đăng ký", padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)

        # Chế độ đăng ký
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill='x', pady=5)

        ttk.Label(mode_frame, text="Chế độ:", style='Header.TLabel').pack(side='left')
        self.mode_var = tk.StringVar(value="auto")
        ttk.Radiobutton(mode_frame, text="Tự động", variable=self.mode_var,
                       value="auto").pack(side='left', padx=10)
        ttk.Radiobutton(mode_frame, text="Thủ công", variable=self.mode_var,
                       value="manual").pack(side='left', padx=10)

        # Số lượng tài khoản
        count_frame = ttk.Frame(config_frame)
        count_frame.pack(fill='x', pady=5)

        ttk.Label(count_frame, text="Số lượng:", style='Header.TLabel').pack(side='left')
        self.count_var = tk.StringVar(value="1")
        count_spin = ttk.Spinbox(count_frame, from_=1, to=100, textvariable=self.count_var, width=10)
        count_spin.pack(side='left', padx=10)

        # Số luồng
        thread_frame = ttk.Frame(config_frame)
        thread_frame.pack(fill='x', pady=5)

        ttk.Label(thread_frame, text="Số luồng:", style='Header.TLabel').pack(side='left')
        self.thread_var = tk.StringVar(value="1")
        thread_spin = ttk.Spinbox(thread_frame, from_=1, to=10, textvariable=self.thread_var, width=10)
        thread_spin.pack(side='left', padx=10)

        # Tùy chọn
        options_frame = ttk.LabelFrame(scrollable_frame, text="Tùy chọn", padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)

        self.use_proxy_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Sử dụng proxy",
                       variable=self.use_proxy_var).pack(anchor='w')

        self.use_existing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Sử dụng trình duyệt hiện có",
                       variable=self.use_existing_var).pack(anchor='w')

        self.save_screenshots_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Lưu screenshots",
                       variable=self.save_screenshots_var).pack(anchor='w')

        # Thông tin thủ công (ẩn mặc định)
        self.manual_frame = ttk.LabelFrame(scrollable_frame, text="Thông tin thủ công", padding=10)

        ttk.Label(self.manual_frame, text="Tên đăng nhập:").grid(row=0, column=0, sticky='w', pady=2)
        self.username_entry = ttk.Entry(self.manual_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=10, pady=2)

        ttk.Label(self.manual_frame, text="Mật khẩu:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = ttk.Entry(self.manual_frame, width=30, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=2)

        ttk.Label(self.manual_frame, text="Họ tên:").grid(row=2, column=0, sticky='w', pady=2)
        self.fullname_entry = ttk.Entry(self.manual_frame, width=30)
        self.fullname_entry.grid(row=2, column=1, padx=10, pady=2)

        # Nút điều khiển
        control_frame = ttk.Frame(scrollable_frame)
        control_frame.pack(fill='x', padx=10, pady=10)

        self.start_btn = ttk.Button(control_frame, text="🚀 Bắt đầu đăng ký",
                                   command=self.start_registration)
        self.start_btn.pack(side='left', padx=5)

        self.stop_btn = ttk.Button(control_frame, text="⏹️ Dừng",
                                  command=self.stop_registration, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

        self.analyze_btn = ttk.Button(control_frame, text="🔍 Phân tích form",
                                     command=self.analyze_form)
        self.analyze_btn.pack(side='left', padx=5)

        # Log output
        log_frame = ttk.LabelFrame(scrollable_frame, text="Log đăng ký", padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill='both', expand=True)

        # Pack canvas và scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mode change
        self.mode_var.trace('w', self.on_mode_change)

    def create_proxy_tab(self):
        """Tab quản lý proxy"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🌐 Proxy")

        # Proxy controls
        control_frame = ttk.LabelFrame(tab_frame, text="Quản lý Proxy", padding=10)
        control_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(control_frame, text="🔄 Tìm proxy mới",
                  command=self.fetch_proxies).pack(side='left', padx=5)
        ttk.Button(control_frame, text="🧪 Test proxy",
                  command=self.test_proxies).pack(side='left', padx=5)
        ttk.Button(control_frame, text="📁 Load từ file",
                  command=self.load_proxy_file).pack(side='left', padx=5)

        # Proxy list
        list_frame = ttk.LabelFrame(tab_frame, text="Danh sách Proxy", padding=10)
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview cho proxy
        columns = ('IP:Port', 'Trạng thái', 'Tốc độ', 'Quốc gia')
        self.proxy_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.proxy_tree.heading(col, text=col)
            self.proxy_tree.column(col, width=150)

        proxy_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.proxy_tree.yview)
        self.proxy_tree.configure(yscrollcommand=proxy_scrollbar.set)

        self.proxy_tree.pack(side='left', fill='both', expand=True)
        proxy_scrollbar.pack(side='right', fill='y')

    def create_settings_tab(self):
        """Tab cài đặt"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="⚙️ Cài đặt")

        # Browser settings
        browser_frame = ttk.LabelFrame(tab_frame, text="Cài đặt trình duyệt", padding=10)
        browser_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(browser_frame, text="Thời gian chờ (giây):").grid(row=0, column=0, sticky='w')
        self.timeout_var = tk.StringVar(value=str(BROWSER_CONFIG.get('timeout', 30)))
        ttk.Entry(browser_frame, textvariable=self.timeout_var, width=10).grid(row=0, column=1, padx=10)

        ttk.Label(browser_frame, text="User Agent:").grid(row=1, column=0, sticky='w')
        self.user_agent_var = tk.StringVar(value=BROWSER_CONFIG.get('user_agent', ''))
        ttk.Entry(browser_frame, textvariable=self.user_agent_var, width=50).grid(row=1, column=1, padx=10)

        # Registration settings
        reg_frame = ttk.LabelFrame(tab_frame, text="Cài đặt đăng ký", padding=10)
        reg_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(reg_frame, text="Delay giữa các bước (giây):").grid(row=0, column=0, sticky='w')
        self.step_delay_var = tk.StringVar(value=str(REGISTRATION_CONFIG.get('step_delay', 2)))
        ttk.Entry(reg_frame, textvariable=self.step_delay_var, width=10).grid(row=0, column=1, padx=10)

        # Save button
        ttk.Button(tab_frame, text="💾 Lưu cài đặt",
                  command=self.save_settings).pack(pady=20)

    def create_results_tab(self):
        """Tab kết quả"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📊 Kết quả")

        # Statistics
        stats_frame = ttk.LabelFrame(tab_frame, text="Thống kê", padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.stats_labels = {}
        stats_info = [
            ('Tổng số tài khoản:', 'total'),
            ('Thành công:', 'success'),
            ('Thất bại:', 'failed'),
            ('Tỷ lệ thành công:', 'rate')
        ]

        for i, (label, key) in enumerate(stats_info):
            ttk.Label(stats_frame, text=label, style='Header.TLabel').grid(row=i//2, column=(i%2)*2, sticky='w', padx=5)
            self.stats_labels[key] = ttk.Label(stats_frame, text="0")
            self.stats_labels[key].grid(row=i//2, column=(i%2)*2+1, sticky='w', padx=5)

        # Results list
        results_frame = ttk.LabelFrame(tab_frame, text="Danh sách kết quả", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview cho kết quả
        result_columns = ('STT', 'Username', 'Password', 'Họ tên', 'Trạng thái', 'Thời gian')
        self.result_tree = ttk.Treeview(results_frame, columns=result_columns, show='headings', height=15)

        for col in result_columns:
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=120)

        result_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=result_scrollbar.set)

        self.result_tree.pack(side='left', fill='both', expand=True)
        result_scrollbar.pack(side='right', fill='y')

        # Export buttons
        export_frame = ttk.Frame(tab_frame)
        export_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(export_frame, text="📤 Xuất Excel",
                  command=self.export_excel).pack(side='left', padx=5)
        ttk.Button(export_frame, text="📄 Xuất CSV",
                  command=self.export_csv).pack(side='left', padx=5)

    def create_status_bar(self):
        """Tạo status bar"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_label = ttk.Label(self.status_frame, text="Sẵn sàng")
        self.status_label.pack(side='left', padx=10)

        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.progress.pack(side='right', padx=10, pady=2)

    def on_mode_change(self, *args):
        """Xử lý khi thay đổi chế độ"""
        if self.mode_var.get() == "manual":
            self.manual_frame.pack(fill='x', padx=10, pady=5)
            self.count_var.set("1")
            self.thread_var.set("1")
        else:
            self.manual_frame.pack_forget()

    def log_message(self, message, level="INFO"):
        """Ghi log vào text widget"""
        timestamp = tk.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Màu sắc theo level
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")

    def update_status(self, message="Sẵn sàng"):
        """Cập nhật status bar"""
        self.status_label.config(text=message)

    def start_registration(self):
        """Bắt đầu đăng ký"""
        if self.is_running:
            return

        # Validation
        try:
            count = int(self.count_var.get())
            threads = int(self.thread_var.get())
            if count <= 0 or threads <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ!")
            return

        # Kiểm tra thông tin thủ công
        if self.mode_var.get() == "manual":
            if not all([self.username_entry.get(), self.password_entry.get(), self.fullname_entry.get()]):
                messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ thông tin!")
                return

        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()

        # Chạy trong thread riêng
        self.registration_thread = threading.Thread(target=self.run_registration_process)
        self.registration_thread.daemon = True
        self.registration_thread.start()

    def stop_registration(self):
        """Dừng đăng ký"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
        self.log_message("Đã dừng đăng ký", "WARNING")

    def run_registration_process(self):
        """Chạy quá trình đăng ký"""
        try:
            self.log_message("Bắt đầu quá trình đăng ký...")

            # Lấy cấu hình
            count = int(self.count_var.get())
            threads = int(self.thread_var.get())
            use_proxy = self.use_proxy_var.get()

            # Chuẩn bị proxy nếu cần
            if use_proxy:
                self.log_message("Đang chuẩn bị proxy...")
                loaded_count = self.proxy_manager.load_working_proxies()
                if loaded_count < 5:
                    self.log_message("Đang tìm proxy mới...")
                    found_count = self.proxy_manager.fetch_and_test_proxies()
                    if found_count == 0:
                        self.log_message("Không tìm thấy proxy! Chạy không proxy.", "WARNING")
                        use_proxy = False

                if use_proxy:
                    self.log_message(f"Sẵn sàng với {len(self.proxy_manager.working_proxies)} proxy")

            # Phân tích form
            self.log_message("Đang phân tích form đăng ký...")
            bot = RegistrationBot()
            selectors = bot.analyze_form()

            if not selectors:
                self.log_message("Không thể phân tích form!", "ERROR")
                return

            self.log_message("Đã phân tích form thành công!")

            # Chuẩn bị thông tin thủ công nếu có
            manual_info = None
            if self.mode_var.get() == "manual":
                manual_info = {
                    'username': self.username_entry.get(),
                    'password': self.password_entry.get(),
                    'full_name': self.fullname_entry.get(),
                    'phone': self.username_entry.get()
                }

            # Chạy đăng ký
            successful = 0
            failed = 0

            for i in range(count):
                if not self.is_running:
                    break

                self.log_message(f"Đăng ký tài khoản #{i+1}/{count}")

                # Lấy proxy
                proxy = None
                if use_proxy:
                    proxy = self.proxy_manager.get_proxy()

                try:
                    # Tạo bot và đăng ký
                    bot = RegistrationBot(proxy)
                    success, account, error_msg = bot.register_account(selectors, manual_info)

                    # Lưu kết quả
                    bot.save_result(success, account, error_msg)

                    if success:
                        successful += 1
                        self.log_message(f"✓ Tài khoản #{i+1}: {account.get('username', 'N/A')} - THÀNH CÔNG", "SUCCESS")
                        self.add_result_to_tree(i+1, account, "Thành công")
                    else:
                        failed += 1
                        self.log_message(f"✗ Tài khoản #{i+1}: THẤT BẠI - {error_msg}", "ERROR")
                        self.add_result_to_tree(i+1, account, f"Thất bại: {error_msg}")

                    # Trả lại proxy
                    if proxy:
                        self.proxy_manager.release_proxy(proxy)

                except Exception as e:
                    failed += 1
                    self.log_message(f"✗ Tài khoản #{i+1}: LỖI - {str(e)}", "ERROR")
                    if proxy:
                        self.proxy_manager.release_proxy(proxy)

                # Cập nhật thống kê
                self.update_statistics(successful + failed, successful, failed)

                # Delay giữa các lần đăng ký
                if i < count - 1 and self.is_running:
                    time.sleep(2)

            # Hoàn thành
            total = successful + failed
            rate = (successful / total * 100) if total > 0 else 0
            self.log_message(f"Hoàn thành! Tổng: {total}, Thành công: {successful}, Thất bại: {failed}, Tỷ lệ: {rate:.1f}%", "SUCCESS")

        except Exception as e:
            self.log_message(f"Lỗi: {str(e)}", "ERROR")
        finally:
            self.is_running = False
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled'),
                self.progress.stop()
            ])

    def analyze_form(self):
        """Phân tích form đăng ký"""
        self.log_message("Đang phân tích form đăng ký...")
        # TODO: Implement form analysis

    def fetch_proxies(self):
        """Tìm proxy mới"""
        def fetch_in_thread():
            try:
                self.log_message("Đang tìm proxy mới...")
                found_count = self.proxy_manager.fetch_and_test_proxies()
                self.log_message(f"Tìm thấy {found_count} proxy hoạt động")
                self.root.after(0, self.update_proxy_tree)
            except Exception as e:
                self.log_message(f"Lỗi tìm proxy: {e}", "ERROR")

        threading.Thread(target=fetch_in_thread, daemon=True).start()

    def test_proxies(self):
        """Test proxy"""
        def test_in_thread():
            try:
                self.log_message("Đang test proxy...")
                # Test lại các proxy hiện có
                working_count = 0
                for proxy in self.proxy_manager.working_proxies[:]:
                    if self.proxy_manager.test_proxy(proxy):
                        working_count += 1
                    else:
                        self.proxy_manager.working_proxies.remove(proxy)

                self.log_message(f"Có {working_count} proxy hoạt động")
                self.root.after(0, self.update_proxy_tree)
            except Exception as e:
                self.log_message(f"Lỗi test proxy: {e}", "ERROR")

        threading.Thread(target=test_in_thread, daemon=True).start()

    def load_proxy_file(self):
        """Load proxy từ file"""
        filename = filedialog.askopenfilename(
            title="Chọn file proxy",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                self.log_message(f"Đang load proxy từ {filename}")
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                loaded_count = 0
                for line in lines:
                    line = line.strip()
                    if ':' in line:
                        try:
                            ip, port = line.split(':')
                            proxy = {'ip': ip.strip(), 'port': int(port.strip())}
                            if self.proxy_manager.test_proxy(proxy):
                                self.proxy_manager.working_proxies.append(proxy)
                                loaded_count += 1
                        except:
                            continue

                self.log_message(f"Đã load {loaded_count} proxy từ file")
                self.update_proxy_tree()

            except Exception as e:
                self.log_message(f"Lỗi load file proxy: {e}", "ERROR")

    def save_settings(self):
        """Lưu cài đặt"""
        messagebox.showinfo("Thông báo", "Đã lưu cài đặt!")

    def export_excel(self):
        """Xuất kết quả ra Excel"""
        filename = filedialog.asksaveasfilename(
            title="Lưu file Excel",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.log_message(f"Đang xuất Excel: {filename}")
            # TODO: Implement Excel export

    def export_csv(self):
        """Xuất kết quả ra CSV"""
        filename = filedialog.asksaveasfilename(
            title="Lưu file CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.log_message(f"Đang xuất CSV: {filename}")
            # TODO: Implement CSV export

    def add_result_to_tree(self, index, account, status):
        """Thêm kết quả vào tree view"""
        try:
            username = account.get('username', 'N/A')
            password = account.get('password', 'N/A')
            fullname = account.get('full_name', 'N/A')
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")

            self.result_tree.insert('', 'end', values=(
                index, username, password, fullname, status, timestamp
            ))
        except Exception as e:
            self.log_message(f"Lỗi thêm kết quả: {e}", "ERROR")

    def update_statistics(self, total, success, failed):
        """Cập nhật thống kê"""
        try:
            rate = (success / total * 100) if total > 0 else 0

            self.stats_labels['total'].config(text=str(total))
            self.stats_labels['success'].config(text=str(success), foreground='green')
            self.stats_labels['failed'].config(text=str(failed), foreground='red')
            self.stats_labels['rate'].config(text=f"{rate:.1f}%")
        except Exception as e:
            self.log_message(f"Lỗi cập nhật thống kê: {e}", "ERROR")

    def update_proxy_tree(self):
        """Cập nhật danh sách proxy"""
        try:
            # Xóa dữ liệu cũ
            for item in self.proxy_tree.get_children():
                self.proxy_tree.delete(item)

            # Thêm proxy mới
            for proxy in self.proxy_manager.working_proxies:
                self.proxy_tree.insert('', 'end', values=(
                    f"{proxy['ip']}:{proxy['port']}",
                    "Hoạt động",
                    f"{proxy.get('speed', 'N/A')}ms",
                    proxy.get('country', 'N/A')
                ))
        except Exception as e:
            self.log_message(f"Lỗi cập nhật proxy: {e}", "ERROR")

    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
