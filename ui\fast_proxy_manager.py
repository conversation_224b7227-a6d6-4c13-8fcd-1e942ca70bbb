"""
Fast Proxy Manager - T<PERSON>i ưu hóa tốc độ cho Simple UI
"""

import requests
import threading
import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

class FastProxyManager:
    def __init__(self):
        self.working_proxies = []
        self.used_proxies = set()
        self.proxy_lock = threading.Lock()
        
        # Proxy sources nhanh và ổn định
        self.fast_sources = [
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
        ]
        
        # C<PERSON>u hình tối ưu
        self.timeout = 5  # Giảm timeout xuống 5s
        self.max_workers = 30  # Giảm số thread
        self.max_proxies_to_test = 50  # Chỉ test 50 proxy đầu tiên

    def log_callback(self, message):
        """Callback để gửi log về UI"""
        if hasattr(self, 'ui_log_callback'):
            self.ui_log_callback(message)
        else:
            print(message)

    def set_ui_callback(self, callback):
        """<PERSON><PERSON><PERSON><PERSON> lập callback để gửi log về UI"""
        self.ui_log_callback = callback

    def fetch_proxies_from_source(self, url):
        """Lấy proxy từ một nguồn - tối ưu hóa"""
        try:
            self.log_callback(f"Đang lấy proxy từ {url.split('/')[-1]}...")
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                proxies = []
                lines = response.text.strip().split('\n')
                
                # Chỉ lấy số lượng giới hạn
                for line in lines[:self.max_proxies_to_test]:
                    line = line.strip()
                    if ':' in line and len(line.split(':')) == 2:
                        ip, port = line.split(':')
                        if self._is_valid_ip(ip) and port.isdigit():
                            proxies.append({'ip': ip, 'port': int(port)})
                
                self.log_callback(f"✓ Lấy được {len(proxies)} proxy")
                return proxies
                
        except Exception as e:
            self.log_callback(f"✗ Lỗi lấy proxy: {str(e)[:50]}...")
        return []

    def _is_valid_ip(self, ip):
        """Kiểm tra IP hợp lệ - nhanh"""
        try:
            parts = ip.split('.')
            return (len(parts) == 4 and 
                   all(0 <= int(part) <= 255 for part in parts) and
                   not ip.startswith('127.') and  # Loại bỏ localhost
                   not ip.startswith('192.168.') and  # Loại bỏ private IP
                   not ip.startswith('10.'))  # Loại bỏ private IP
        except:
            return False

    def test_proxy(self, proxy):
        """Test một proxy - tối ưu hóa"""
        try:
            proxy_url = f"http://{proxy['ip']}:{proxy['port']}"
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url
            }

            # Sử dụng endpoint nhanh hơn
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=self.timeout
            )

            if response.status_code == 200:
                # Thêm thông tin tốc độ
                proxy['speed'] = self.timeout * 1000  # Estimate speed
                proxy['country'] = 'Unknown'
                return proxy
                
        except:
            pass
        return None

    def quick_fetch_and_test(self):
        """Lấy và test proxy nhanh - chỉ lấy đủ dùng"""
        self.log_callback("🚀 Bắt đầu tìm proxy nhanh...")
        
        all_proxies = []
        
        # Chỉ lấy từ 1-2 nguồn đầu tiên
        for source in self.fast_sources[:2]:
            proxies = self.fetch_proxies_from_source(source)
            all_proxies.extend(proxies)
            
            # Nếu đã có đủ proxy để test thì dừng
            if len(all_proxies) >= self.max_proxies_to_test:
                break

        if not all_proxies:
            self.log_callback("❌ Không lấy được proxy nào")
            return 0

        # Shuffle để test ngẫu nhiên
        random.shuffle(all_proxies)
        all_proxies = all_proxies[:self.max_proxies_to_test]
        
        self.log_callback(f"🧪 Đang test {len(all_proxies)} proxy...")
        
        working_proxies = []
        tested_count = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_proxy = {executor.submit(self.test_proxy, proxy): proxy for proxy in all_proxies}

            for future in as_completed(future_to_proxy):
                tested_count += 1
                result = future.result()
                
                if result:
                    working_proxies.append(result)
                    self.log_callback(f"✓ Proxy #{len(working_proxies)}: {result['ip']}:{result['port']}")
                
                # Cập nhật tiến trình
                if tested_count % 10 == 0:
                    self.log_callback(f"📊 Đã test {tested_count}/{len(all_proxies)} proxy...")
                
                # Dừng sớm nếu đã có đủ proxy
                if len(working_proxies) >= 10:  # Chỉ cần 10 proxy là đủ
                    self.log_callback("✅ Đã có đủ proxy, dừng test...")
                    break

        self.working_proxies = working_proxies
        self.log_callback(f"🎉 Hoàn thành! Tìm được {len(working_proxies)} proxy hoạt động")

        # Lưu proxy
        self.save_working_proxies()
        
        return len(working_proxies)

    def get_proxy(self):
        """Lấy một proxy chưa sử dụng"""
        with self.proxy_lock:
            available_proxies = [p for p in self.working_proxies if f"{p['ip']}:{p['port']}" not in self.used_proxies]

            if not available_proxies:
                self.log_callback("⚠️ Hết proxy khả dụng!")
                return None

            proxy = random.choice(available_proxies)
            self.used_proxies.add(f"{proxy['ip']}:{proxy['port']}")
            return proxy

    def release_proxy(self, proxy):
        """Trả lại proxy để sử dụng lại"""
        with self.proxy_lock:
            proxy_key = f"{proxy['ip']}:{proxy['port']}"
            if proxy_key in self.used_proxies:
                self.used_proxies.remove(proxy_key)

    def save_working_proxies(self):
        """Lưu danh sách proxy hoạt động"""
        try:
            proxy_file = Path(__file__).parent.parent / "data" / "proxies" / "working_proxies.txt"
            proxy_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(proxy_file, 'w', encoding='utf-8') as f:
                for proxy in self.working_proxies:
                    f.write(f"{proxy['ip']}:{proxy['port']}\n")
            
            self.log_callback(f"💾 Đã lưu {len(self.working_proxies)} proxy")
            
        except Exception as e:
            self.log_callback(f"❌ Lỗi lưu proxy: {e}")

    def load_working_proxies(self):
        """Load proxy đã lưu"""
        try:
            proxy_file = Path(__file__).parent.parent / "data" / "proxies" / "working_proxies.txt"
            
            if not proxy_file.exists():
                self.log_callback("📁 Không tìm thấy file proxy đã lưu")
                return 0
            
            with open(proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.working_proxies = []
            for line in lines:
                line = line.strip()
                if ':' in line:
                    ip, port = line.split(':')
                    self.working_proxies.append({
                        'ip': ip,
                        'port': int(port),
                        'speed': 5000,
                        'country': 'Cached'
                    })
            
            self.log_callback(f"📂 Đã load {len(self.working_proxies)} proxy từ file")
            return len(self.working_proxies)
            
        except Exception as e:
            self.log_callback(f"❌ Lỗi load proxy: {e}")
            return 0

    def get_proxy_info(self):
        """Lấy thông tin proxy để hiển thị"""
        return {
            'total': len(self.working_proxies),
            'used': len(self.used_proxies),
            'available': len(self.working_proxies) - len(self.used_proxies)
        }

# Test function
if __name__ == "__main__":
    manager = FastProxyManager()
    
    # Test load
    loaded = manager.load_working_proxies()
    print(f"Loaded: {loaded}")
    
    if loaded < 5:
        # Test fetch
        found = manager.quick_fetch_and_test()
        print(f"Found: {found}")
    
    # Test get proxy
    proxy = manager.get_proxy()
    if proxy:
        print(f"Got proxy: {proxy}")
        manager.release_proxy(proxy)
