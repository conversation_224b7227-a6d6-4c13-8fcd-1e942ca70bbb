"""
Console Launcher - Chạy tool trong terminal
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def show_menu():
    """Hiển thị menu console"""
    print("🚀 Chrome Auto Registration Tool v2.0")
    print("=" * 50)
    print()
    print("Chọn tùy chọn:")
    print("1. 🖥️  Chạy Simple UI")
    print("2. 🧪  Test UI")
    print("3. 💻  Console mode (nếu có)")
    print("4. 📋  Xem cấu trúc project")
    print("5. ❌  Thoát")
    print()

def show_project_structure():
    """Hiển thị cấu trúc project"""
    print("\n📁 Cấu trúc project:")
    print("=" * 40)
    
    current_dir = Path(__file__).parent
    
    important_files = [
        "simple_launcher.py",
        "test_simple_ui.py", 
        "ui/simple_ui.py",
        "ui/main_window.py",
        "src/main.py",
        "src/config.py",
        "data/",
        "temp/"
    ]
    
    for file_path in important_files:
        full_path = current_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
    
    print("\n📋 Files khởi động:")
    print("- start_simple.bat")
    print("- test_simple.bat") 
    print("- simple_launcher.py")
    print("- test_simple_ui.py")

def run_simple_ui():
    """Chạy Simple UI"""
    try:
        print("\n🚀 Đang khởi động Simple UI...")
        
        # Import và chạy
        current_dir = Path(__file__).parent
        ui_path = str(current_dir / "ui")
        
        if ui_path not in sys.path:
            sys.path.insert(0, ui_path)
        
        from simple_ui import SimpleUI
        app = SimpleUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        print("Vui lòng đảm bảo file ui/simple_ui.py tồn tại")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def run_test():
    """Chạy test"""
    try:
        print("\n🧪 Đang chạy test...")
        
        current_dir = Path(__file__).parent
        ui_path = str(current_dir / "ui")
        
        if ui_path not in sys.path:
            sys.path.insert(0, ui_path)
        
        from simple_ui import SimpleUI
        
        print("✅ Import Simple UI thành công!")
        
        # Tạo và test UI
        app = SimpleUI()
        print("✅ Khởi tạo UI thành công!")
        
        # Chạy UI
        app.run()
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")

def run_console():
    """Chạy console mode"""
    try:
        print("\n💻 Console mode...")
        print("Tính năng này đang được phát triển.")
        print("Hiện tại hãy sử dụng Simple UI.")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def main():
    """Hàm main"""
    setup_environment()
    
    while True:
        show_menu()
        
        try:
            choice = input("Nhập lựa chọn (1-5): ").strip()
            
            if choice == "1":
                run_simple_ui()
                break
            
            elif choice == "2":
                run_test()
                break
            
            elif choice == "3":
                run_console()
                input("\nNhấn Enter để tiếp tục...")
            
            elif choice == "4":
                show_project_structure()
                input("\nNhấn Enter để tiếp tục...")
            
            elif choice == "5":
                print("👋 Tạm biệt!")
                break
            
            else:
                print("❌ Lựa chọn không hợp lệ!")
                input("Nhấn Enter để tiếp tục...")
        
        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            input("Nhấn Enter để tiếp tục...")

if __name__ == "__main__":
    main()
