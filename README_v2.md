# 🚀 Chrome Auto Registration Tool v2.0

Tool tự động đăng ký tài khoản 13win16.com với giao diện UI hiện đại và hỗ trợ proxy

## ✨ Tính năng mới v2.0

### 🖥️ Giao diện UI
- **Giao diện đồ họa hiện đại** với tkinter
- **Tabs tổ chức rõ ràng**: <PERSON><PERSON><PERSON> ký, Proxy, <PERSON><PERSON><PERSON> đặt, Kết quả
- **Real-time logging** và theo dõi tiến trình
- **Xuất kết quả** ra Excel/CSV
- **Quản lý proxy** trực quan

### 🔧 Cải tiến kỹ thuật
- **Cấu trúc folder tối ưu** và dễ quản lý
- **Đa chế độ**: UI hoặc Console
- **Tự động kiểm tra dependencies**
- **<PERSON><PERSON> lý lỗi tốt hơn**

### 🎯 Tính năng cốt lõi
- ✅ Tự động đăng ký tài khoản với thông tin ngẫu nhiên
- 📝 **<PERSON>h<PERSON><PERSON> thông tin thủ công** (tên đăng nhập, mật khẩu, họ tên thật)
- 🌐 Sử dụng proxy miễn phí khác nhau cho mỗi trình duyệt
- 🔄 Chạy đa luồng (nhiều trình duyệt đồng thời)
- 🖥️ **Kết nối với trình duyệt hiện có** của máy để auto điền
- 📊 Theo dõi kết quả real-time
- 💾 Lưu thông tin tài khoản thành công
- 🔍 Tự động phân tích form đăng ký
- 📸 Chụp screenshot để debug

## 📁 Cấu trúc project

```
chrome-auto/
├── launcher.py              # File khởi động chính
├── requirements.txt         # Dependencies
├── README_v2.md            # Hướng dẫn v2.0
├── src/                    # Source code chính
│   ├── main.py            # Tool console (phiên bản cũ)
│   ├── config.py          # Cấu hình
│   ├── proxy_manager.py   # Quản lý proxy
│   ├── registration_bot.py # Bot đăng ký
│   └── account_generator.py # Tạo tài khoản
├── ui/                     # Giao diện UI
│   └── main_window.py     # Cửa sổ chính
├── data/                   # Dữ liệu
│   ├── accounts/          # File tài khoản
│   ├── logs/              # File log
│   ├── screenshots/       # Ảnh chụp màn hình
│   └── proxies/           # File proxy
├── temp/                   # File tạm
│   └── chrome_profiles/   # Profile Chrome
└── drivers/                # ChromeDriver
    └── chromedriver.exe
```

## 🚀 Cài đặt

### 1. Yêu cầu hệ thống
- **Python 3.8+**
- **Windows 10/11** (khuyến nghị)
- **Chrome Browser**

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 3. Tải ChromeDriver
- Tải từ: https://chromedriver.chromium.org/
- Đặt vào thư mục `drivers/`

## 🎯 Sử dụng

### Khởi động tool
```bash
python launcher.py
```

### Chọn chế độ:
1. **🖥️ Giao diện UI** - Dễ sử dụng, trực quan
2. **💻 Console** - Chạy trong terminal (như phiên bản cũ)

### Chế độ UI:
1. **Tab Đăng ký**: Cấu hình và chạy đăng ký
   - Chọn chế độ: Tự động hoặc Thủ công
   - Thiết lập số lượng tài khoản và luồng
   - Tùy chọn proxy và trình duyệt
   - Theo dõi log real-time

2. **Tab Proxy**: Quản lý proxy
   - Tìm proxy mới tự động
   - Test tốc độ proxy
   - Load proxy từ file
   - Xem danh sách proxy hoạt động

3. **Tab Cài đặt**: Tùy chỉnh cấu hình
   - Timeout, User Agent
   - Delay giữa các bước
   - Cài đặt trình duyệt

4. **Tab Kết quả**: Xem thống kê và xuất file
   - Thống kê thành công/thất bại
   - Danh sách tài khoản chi tiết
   - Xuất Excel/CSV

### Chế độ Console:
- Giống phiên bản cũ
- Chạy trong terminal
- Tương tác bằng text

## ⚙️ Cấu hình

### File `src/config.py`:
- **REGISTER_URL**: URL đăng ký
- **BROWSER_CONFIG**: Cài đặt trình duyệt
- **PROXY_CONFIG**: Cài đặt proxy
- **FORM_SELECTORS**: Selectors cho form

### Tùy chỉnh trong UI:
- Timeout, User Agent
- Delay giữa các bước
- Cài đặt proxy

## 📊 Kết quả

### File output:
- `data/accounts/successful_accounts.txt`: Tài khoản thành công
- `data/accounts/failed_accounts.txt`: Tài khoản thất bại  
- `data/logs/registration.log`: Log chi tiết
- `data/screenshots/`: Ảnh chụp màn hình

### Xuất dữ liệu:
- **Excel (.xlsx)**: Bảng tính đầy đủ
- **CSV (.csv)**: Dữ liệu thô

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **"Thiếu dependencies"**
   ```bash
   pip install -r requirements.txt
   ```

2. **"ChromeDriver not found"**
   - Tải ChromeDriver phù hợp với phiên bản Chrome
   - Đặt vào thư mục `drivers/`

3. **"Không kết nối được proxy"**
   - Kiểm tra kết nối internet
   - Thử tìm proxy mới trong tab Proxy

4. **"Form selectors không hoạt động"**
   - Website có thể đã thay đổi
   - Sử dụng chức năng "Phân tích form" để cập nhật

5. **"UI không hiển thị"**
   - Đảm bảo đã cài tkinter: `pip install tk`
   - Thử chế độ Console thay thế

## 🆕 Changelog v2.0

- ✅ Thêm giao diện UI hoàn chỉnh
- ✅ Tổ chức lại cấu trúc folder
- ✅ Cải thiện quản lý proxy
- ✅ Thêm xuất Excel/CSV
- ✅ Real-time logging
- ✅ Tự động kiểm tra dependencies
- ✅ Dual mode: UI + Console
- ✅ Xóa bỏ code C# không cần thiết
- ✅ Tối ưu hóa performance

## 🎮 Hướng dẫn nhanh

### Lần đầu sử dụng:
1. `python launcher.py`
2. Chọn "🖥️ Giao diện UI"
3. Tab "Đăng ký" → Chọn chế độ "Tự động"
4. Nhập số lượng tài khoản
5. Bấm "🚀 Bắt đầu đăng ký"

### Sử dụng proxy:
1. Tab "Proxy" → "🔄 Tìm proxy mới"
2. Chờ tìm và test proxy
3. Quay lại tab "Đăng ký"
4. Tick "Sử dụng proxy"

### Nhập thông tin thủ công:
1. Tab "Đăng ký" → Chọn "Thủ công"
2. Nhập username, password, họ tên
3. Bấm "🚀 Bắt đầu đăng ký"

## ⚠️ Lưu ý quan trọng

- **Chỉ sử dụng cho mục đích học tập và nghiên cứu**
- **Tuân thủ Terms of Service của website**
- **Không spam hoặc lạm dụng**
- **Sử dụng có trách nhiệm**

## 👨‍💻 Tác giả

**EderGhostVN** - Tool tự động đăng ký tài khoản

## 📄 License

Chỉ sử dụng cho mục đích học tập và nghiên cứu.

---

**🎉 Cảm ơn bạn đã sử dụng Chrome Auto Registration Tool v2.0!**
