"""
Simple Launcher - Chỉ tập trung vào Simple UI
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import subprocess

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def check_dependencies():
    """Kiểm tra dependencies cơ bản"""
    required_packages = ['tkinter']
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Thiếu packages: {missing_packages}")
        return False
    return True

def launch_simple_ui():
    """Khởi động Simple UI"""
    try:
        # Thêm ui folder vào path
        current_dir = Path(__file__).parent
        ui_path = str(current_dir / "ui")
        
        if ui_path not in sys.path:
            sys.path.insert(0, ui_path)
        
        print("🚀 Đang khởi động Simple UI...")
        
        from simple_ui import SimpleUI
        app = SimpleUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        print("Vui lòng đảm bảo file ui/simple_ui.py tồn tại")
        input("Nhấn Enter để thoát...")
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        input("Nhấn Enter để thoát...")

def launch_console():
    """Khởi động console mode"""
    try:
        print("🚀 Đang khởi động Console mode...")
        
        # Chạy test_simple_ui.py thay vì main.py phức tạp
        current_dir = Path(__file__).parent
        test_file = current_dir / "test_simple_ui.py"
        
        if test_file.exists():
            subprocess.run([sys.executable, str(test_file)])
        else:
            print("❌ Không tìm thấy test_simple_ui.py")
            input("Nhấn Enter để thoát...")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        input("Nhấn Enter để thoát...")

def show_menu():
    """Hiển thị menu chọn"""
    root = tk.Tk()
    root.title("Chrome Auto Registration Tool - Simple Launcher")
    root.geometry("500x400")
    root.resizable(False, False)
    
    # Center window
    root.eval('tk::PlaceWindow . center')
    
    # Header
    header_frame = tk.Frame(root, bg='#2E86AB', height=80)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)
    
    title_label = tk.Label(header_frame, text="🚀 Chrome Auto Registration Tool", 
                          font=('Arial', 16, 'bold'), fg='white', bg='#2E86AB')
    title_label.pack(expand=True)
    
    subtitle_label = tk.Label(header_frame, text="v2.0 - Simple Launcher", 
                             font=('Arial', 10), fg='white', bg='#2E86AB')
    subtitle_label.pack()
    
    # Main content
    content_frame = tk.Frame(root, padx=30, pady=30)
    content_frame.pack(fill='both', expand=True)
    
    info_label = tk.Label(content_frame, 
                         text="Chọn chế độ khởi động:", 
                         font=('Arial', 14, 'bold'))
    info_label.pack(pady=20)
    
    # Buttons
    button_frame = tk.Frame(content_frame)
    button_frame.pack(expand=True)
    
    def start_simple_ui():
        root.destroy()
        launch_simple_ui()
    
    def start_console():
        root.destroy()
        launch_console()
    
    def start_test():
        root.destroy()
        # Chạy test trực tiếp
        try:
            subprocess.run([sys.executable, "test_simple_ui.py"])
        except:
            print("❌ Không thể chạy test")
            input("Nhấn Enter để thoát...")
    
    # Simple UI button
    ui_btn = tk.Button(button_frame, text="🖥️ Simple UI", 
                      command=start_simple_ui, width=25, height=3,
                      font=('Arial', 12, 'bold'), bg='#28A745', fg='white')
    ui_btn.pack(pady=10)
    
    # Test button
    test_btn = tk.Button(button_frame, text="🧪 Test UI", 
                        command=start_test, width=25, height=2,
                        font=('Arial', 10), bg='#FFC107', fg='black')
    test_btn.pack(pady=5)
    
    # Console button
    console_btn = tk.Button(button_frame, text="💻 Console Mode", 
                           command=start_console, width=25, height=2,
                           font=('Arial', 10), bg='#17A2B8', fg='white')
    console_btn.pack(pady=5)
    
    # Exit button
    exit_btn = tk.Button(button_frame, text="❌ Thoát", 
                        command=root.destroy, width=25, height=2,
                        font=('Arial', 10), bg='#DC3545', fg='white')
    exit_btn.pack(pady=10)
    
    # Info
    info_text = """
🖥️ Simple UI: Giao diện đơn giản, ổn định
🧪 Test UI: Chạy test để kiểm tra
💻 Console: Chạy trong terminal
    """
    
    info_label2 = tk.Label(content_frame, text=info_text, 
                          font=('Arial', 9), fg='gray', justify='left')
    info_label2.pack(pady=20)
    
    # Status
    status_label = tk.Label(content_frame, text="✅ Sẵn sàng", 
                           font=('Arial', 8), fg='green')
    status_label.pack()
    
    root.mainloop()

def main():
    """Hàm main"""
    print("🚀 Chrome Auto Registration Tool - Simple Launcher")
    print("=" * 60)
    
    # Thiết lập môi trường
    setup_environment()
    
    # Kiểm tra dependencies
    if not check_dependencies():
        print("❌ Thiếu dependencies")
        input("Nhấn Enter để thoát...")
        return
    
    # Kiểm tra file cần thiết
    current_dir = Path(__file__).parent
    simple_ui_file = current_dir / "ui" / "simple_ui.py"
    test_file = current_dir / "test_simple_ui.py"
    
    if not simple_ui_file.exists():
        print("❌ Không tìm thấy ui/simple_ui.py")
        input("Nhấn Enter để thoát...")
        return
    
    if not test_file.exists():
        print("❌ Không tìm thấy test_simple_ui.py")
        input("Nhấn Enter để thoát...")
        return
    
    print("✅ Tất cả file cần thiết đã sẵn sàng")
    
    # Hiển thị menu
    try:
        show_menu()
    except Exception as e:
        print(f"❌ Lỗi launcher: {e}")
        print("\n🔄 Thử chạy trực tiếp:")
        print("python test_simple_ui.py")
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
