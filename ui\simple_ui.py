"""
Simple UI version để test trước
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys
import os
import time
import datetime
from pathlib import Path

class SimpleUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Chrome Auto Registration Tool v2.0 - Simple UI")
        self.root.geometry("800x600")
        
        # Thiết lập style
        self.setup_style()
        
        # Biến trạng thái
        self.is_running = False
        
        # Tạo giao diện
        self.create_widgets()

    def setup_style(self):
        """Thiết lập style"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_widgets(self):
        """Tạo widgets"""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(header_frame, text="🚀 Chrome Auto Registration Tool v2.0", 
                               font=('Arial', 14, 'bold'), foreground='#2E86AB')
        title_label.pack()
        
        # Main content
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Configuration
        config_frame = ttk.LabelFrame(main_frame, text="Cấu hình", padding=10)
        config_frame.pack(fill='x', pady=5)
        
        # Mode selection
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill='x', pady=5)
        
        ttk.Label(mode_frame, text="Chế độ:").pack(side='left')
        self.mode_var = tk.StringVar(value="auto")
        ttk.Radiobutton(mode_frame, text="Tự động", variable=self.mode_var, 
                       value="auto", command=self.on_mode_change).pack(side='left', padx=10)
        ttk.Radiobutton(mode_frame, text="Thủ công", variable=self.mode_var, 
                       value="manual", command=self.on_mode_change).pack(side='left', padx=10)
        
        # Count
        count_frame = ttk.Frame(config_frame)
        count_frame.pack(fill='x', pady=5)
        
        ttk.Label(count_frame, text="Số lượng:").pack(side='left')
        self.count_var = tk.StringVar(value="1")
        ttk.Spinbox(count_frame, from_=1, to=100, textvariable=self.count_var, width=10).pack(side='left', padx=10)
        
        # Options
        options_frame = ttk.Frame(config_frame)
        options_frame.pack(fill='x', pady=5)
        
        self.use_proxy_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Sử dụng proxy", 
                       variable=self.use_proxy_var).pack(side='left', padx=10)
        
        self.use_existing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Sử dụng trình duyệt hiện có", 
                       variable=self.use_existing_var).pack(side='left', padx=10)
        
        # Manual info frame (hidden by default)
        self.manual_frame = ttk.LabelFrame(main_frame, text="Thông tin thủ công", padding=10)
        
        ttk.Label(self.manual_frame, text="Tên đăng nhập:").grid(row=0, column=0, sticky='w', pady=2)
        self.username_entry = ttk.Entry(self.manual_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=10, pady=2)
        
        ttk.Label(self.manual_frame, text="Mật khẩu:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = ttk.Entry(self.manual_frame, width=30, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=2)
        
        ttk.Label(self.manual_frame, text="Họ tên:").grid(row=2, column=0, sticky='w', pady=2)
        self.fullname_entry = ttk.Entry(self.manual_frame, width=30)
        self.fullname_entry.grid(row=2, column=1, padx=10, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x', pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="🚀 Bắt đầu", 
                                   command=self.start_process)
        self.start_btn.pack(side='left', padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ Dừng", 
                                  command=self.stop_process, state='disabled')
        self.stop_btn.pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="🔍 Test", 
                  command=self.test_function).pack(side='left', padx=5)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding=10)
        log_frame.pack(fill='both', expand=True, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill='both', expand=True)
        
        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')
        
        self.status_label = ttk.Label(self.status_frame, text="Sẵn sàng")
        self.status_label.pack(side='left', padx=10)
        
        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.progress.pack(side='right', padx=10, pady=2)

    def on_mode_change(self):
        """Xử lý khi thay đổi chế độ"""
        if self.mode_var.get() == "manual":
            self.manual_frame.pack(fill='x', pady=5)
            self.count_var.set("1")
        else:
            self.manual_frame.pack_forget()

    def log_message(self, message, level="INFO"):
        """Ghi log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Màu sắc
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")

    def update_status(self, message):
        """Cập nhật status"""
        self.status_label.config(text=message)

    def start_process(self):
        """Bắt đầu quá trình"""
        if self.is_running:
            return
        
        # Validation
        try:
            count = int(self.count_var.get())
            if count <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ!")
            return
        
        # Kiểm tra thông tin thủ công
        if self.mode_var.get() == "manual":
            if not all([self.username_entry.get(), self.password_entry.get(), self.fullname_entry.get()]):
                messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ thông tin!")
                return
        
        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        self.update_status("Đang chạy...")
        
        # Chạy trong thread riêng
        thread = threading.Thread(target=self.run_process, daemon=True)
        thread.start()

    def stop_process(self):
        """Dừng quá trình"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
        self.update_status("Đã dừng")
        self.log_message("Đã dừng bởi người dùng", "WARNING")

    def run_process(self):
        """Chạy quá trình chính"""
        try:
            count = int(self.count_var.get())
            mode = self.mode_var.get()
            use_proxy = self.use_proxy_var.get()
            use_existing = self.use_existing_var.get()
            
            self.log_message("Bắt đầu quá trình đăng ký...")
            self.log_message(f"Chế độ: {mode}")
            self.log_message(f"Số lượng: {count}")
            self.log_message(f"Sử dụng proxy: {use_proxy}")
            self.log_message(f"Trình duyệt hiện có: {use_existing}")
            
            if mode == "manual":
                username = self.username_entry.get()
                password = self.password_entry.get()
                fullname = self.fullname_entry.get()
                self.log_message(f"Thông tin thủ công: {username} - {fullname}")
            
            # Simulate work
            for i in range(count):
                if not self.is_running:
                    break
                
                self.log_message(f"Đang xử lý tài khoản #{i+1}/{count}")
                
                # Simulate processing time
                for j in range(10):
                    if not self.is_running:
                        break
                    time.sleep(0.5)
                
                if self.is_running:
                    # Simulate success/failure
                    import random
                    if random.random() > 0.3:  # 70% success rate
                        self.log_message(f"✓ Tài khoản #{i+1}: THÀNH CÔNG", "SUCCESS")
                    else:
                        self.log_message(f"✗ Tài khoản #{i+1}: THẤT BẠI", "ERROR")
            
            if self.is_running:
                self.log_message("Hoàn thành tất cả!", "SUCCESS")
            
        except Exception as e:
            self.log_message(f"Lỗi: {str(e)}", "ERROR")
        finally:
            self.is_running = False
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled'),
                self.progress.stop(),
                self.update_status("Hoàn thành")
            ])

    def test_function(self):
        """Test function"""
        self.log_message("Test function được gọi!", "INFO")
        self.log_message("Kiểm tra các module...", "INFO")
        
        # Test import
        try:
            sys.path.append(str(Path(__file__).parent.parent / "src"))
            from config import REGISTRATION_CONFIG
            self.log_message("✓ Import config thành công", "SUCCESS")
        except Exception as e:
            self.log_message(f"✗ Lỗi import config: {e}", "ERROR")
        
        try:
            from proxy_manager import ProxyManager
            self.log_message("✓ Import proxy_manager thành công", "SUCCESS")
        except Exception as e:
            self.log_message(f"✗ Lỗi import proxy_manager: {e}", "ERROR")
        
        self.log_message("Test hoàn thành!", "INFO")

    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleUI()
    app.run()
