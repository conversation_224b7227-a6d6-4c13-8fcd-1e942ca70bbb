"""
Simple UI version để test trước
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys
import os
import time
import datetime
from pathlib import Path

class SimpleUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Chrome Auto Registration Tool v2.0 - Simple UI")
        self.root.geometry("800x600")

        # Thiết lập style
        self.setup_style()

        # Biến trạng thái
        self.is_running = False

        # Tạo giao diện
        self.create_widgets()

    def setup_style(self):
        """Thiết lập style"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_widgets(self):
        """Tạo widgets"""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', padx=10, pady=5)

        title_label = ttk.Label(header_frame, text="🚀 Chrome Auto Registration Tool v2.0",
                               font=('Arial', 14, 'bold'), foreground='#2E86AB')
        title_label.pack()

        # Main content
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Configuration
        config_frame = ttk.LabelFrame(main_frame, text="Cấu hình", padding=10)
        config_frame.pack(fill='x', pady=5)

        # Mode selection
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill='x', pady=5)

        ttk.Label(mode_frame, text="Chế độ:").pack(side='left')
        self.mode_var = tk.StringVar(value="auto")
        ttk.Radiobutton(mode_frame, text="Tự động", variable=self.mode_var,
                       value="auto", command=self.on_mode_change).pack(side='left', padx=10)
        ttk.Radiobutton(mode_frame, text="Thủ công", variable=self.mode_var,
                       value="manual", command=self.on_mode_change).pack(side='left', padx=10)

        # Count
        count_frame = ttk.Frame(config_frame)
        count_frame.pack(fill='x', pady=5)

        ttk.Label(count_frame, text="Số lượng:").pack(side='left')
        self.count_var = tk.StringVar(value="1")
        ttk.Spinbox(count_frame, from_=1, to=100, textvariable=self.count_var, width=10).pack(side='left', padx=10)

        # Options
        options_frame = ttk.Frame(config_frame)
        options_frame.pack(fill='x', pady=5)

        self.use_proxy_var = tk.BooleanVar(value=False)  # Mặc định tắt proxy để nhanh hơn
        proxy_cb = ttk.Checkbutton(options_frame, text="Sử dụng proxy (chậm)",
                                  variable=self.use_proxy_var)
        proxy_cb.pack(side='left', padx=10)

        self.use_existing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Sử dụng trình duyệt hiện có",
                       variable=self.use_existing_var).pack(side='left', padx=10)

        # Quick mode option
        self.quick_mode_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Chế độ nhanh (bỏ qua proxy)",
                       variable=self.quick_mode_var,
                       command=self.on_quick_mode_change).pack(side='left', padx=10)

        # Manual info frame (hidden by default)
        self.manual_frame = ttk.LabelFrame(main_frame, text="Thông tin thủ công", padding=10)

        ttk.Label(self.manual_frame, text="Tên đăng nhập:").grid(row=0, column=0, sticky='w', pady=2)
        self.username_entry = ttk.Entry(self.manual_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=10, pady=2)

        ttk.Label(self.manual_frame, text="Mật khẩu:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = ttk.Entry(self.manual_frame, width=30, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=2)

        ttk.Label(self.manual_frame, text="Họ tên:").grid(row=2, column=0, sticky='w', pady=2)
        self.fullname_entry = ttk.Entry(self.manual_frame, width=30)
        self.fullname_entry.grid(row=2, column=1, padx=10, pady=2)

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x', pady=10)

        self.start_btn = ttk.Button(control_frame, text="🚀 Bắt đầu",
                                   command=self.start_process)
        self.start_btn.pack(side='left', padx=5)

        self.stop_btn = ttk.Button(control_frame, text="⏹️ Dừng",
                                  command=self.stop_process, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

        ttk.Button(control_frame, text="🔍 Test",
                  command=self.test_function).pack(side='left', padx=5)

        # Results area
        results_frame = ttk.LabelFrame(main_frame, text="Kết quả đăng ký", padding=10)
        results_frame.pack(fill='x', pady=5)

        # Results table
        columns = ('STT', 'Username', 'Password', 'Họ tên', 'Ngân hàng', 'Trạng thái')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=6)

        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == 'STT':
                self.results_tree.column(col, width=50)
            elif col == 'Password':
                self.results_tree.column(col, width=100)
            else:
                self.results_tree.column(col, width=120)

        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side='left', fill='both', expand=True)
        results_scrollbar.pack(side='right', fill='y')

        # Results control buttons
        results_control_frame = ttk.Frame(results_frame)
        results_control_frame.pack(fill='x', pady=5)

        ttk.Button(results_control_frame, text="📤 Xuất file",
                  command=self.export_results).pack(side='left', padx=5)
        ttk.Button(results_control_frame, text="🗑️ Xóa kết quả",
                  command=self.clear_results).pack(side='left', padx=5)

        # Results info
        self.results_info_label = ttk.Label(results_control_frame, text="Chưa có kết quả")
        self.results_info_label.pack(side='right', padx=5)

        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding=10)
        log_frame.pack(fill='both', expand=True, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10)
        self.log_text.pack(fill='both', expand=True)

        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_label = ttk.Label(self.status_frame, text="Sẵn sàng")
        self.status_label.pack(side='left', padx=10)

        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.progress.pack(side='right', padx=10, pady=2)

    def on_mode_change(self):
        """Xử lý khi thay đổi chế độ"""
        if self.mode_var.get() == "manual":
            self.manual_frame.pack(fill='x', pady=5)
            self.count_var.set("1")
        else:
            self.manual_frame.pack_forget()

    def on_quick_mode_change(self):
        """Xử lý khi thay đổi chế độ nhanh"""
        if self.quick_mode_var.get():
            self.use_proxy_var.set(False)
            self.log_message("🚀 Chế độ nhanh: Tắt proxy để tăng tốc", "INFO")
        else:
            self.log_message("🐌 Chế độ thường: Có thể bật proxy", "INFO")

    def log_message(self, message, level="INFO"):
        """Ghi log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Màu sắc
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")

    def update_status(self, message):
        """Cập nhật status"""
        self.status_label.config(text=message)

    def start_process(self):
        """Bắt đầu quá trình"""
        if self.is_running:
            return

        # Validation
        try:
            count = int(self.count_var.get())
            if count <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ!")
            return

        # Kiểm tra thông tin thủ công
        if self.mode_var.get() == "manual":
            if not all([self.username_entry.get(), self.password_entry.get(), self.fullname_entry.get()]):
                messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ thông tin!")
                return

        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        self.update_status("Đang chạy...")

        # Chạy trong thread riêng
        thread = threading.Thread(target=self.run_process, daemon=True)
        thread.start()

    def stop_process(self):
        """Dừng quá trình"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
        self.update_status("Đã dừng")
        self.log_message("Đã dừng bởi người dùng", "WARNING")

    def run_process(self):
        """Chạy quá trình chính - Tích hợp với logic thực tế"""
        try:
            count = int(self.count_var.get())
            mode = self.mode_var.get()
            use_proxy = self.use_proxy_var.get() and not self.quick_mode_var.get()  # Tắt proxy nếu chế độ nhanh
            use_existing = self.use_existing_var.get()
            quick_mode = self.quick_mode_var.get()

            self.log_message("🚀 Bắt đầu quá trình đăng ký...")
            self.log_message(f"📋 Chế độ: {mode}")
            self.log_message(f"🔢 Số lượng: {count}")
            self.log_message(f"🌐 Sử dụng proxy: {use_proxy}")
            self.log_message(f"🖥️ Trình duyệt hiện có: {use_existing}")
            self.log_message(f"⚡ Chế độ nhanh: {quick_mode}")

            # Chuẩn bị thông tin thủ công
            manual_info = None
            if mode == "manual":
                username = self.username_entry.get()
                password = self.password_entry.get()
                fullname = self.fullname_entry.get()
                manual_info = {
                    'username': username,
                    'password': password,
                    'full_name': fullname,
                    'phone': username
                }
                self.log_message(f"Thông tin thủ công: {username} - {fullname}")

            # Import các module cần thiết
            try:
                sys.path.append(str(Path(__file__).parent.parent / "src"))

                # Import Fast Proxy Manager từ UI folder
                from fast_proxy_manager import FastProxyManager

                # Import modules từ src
                from registration_bot import RegistrationBot
                from account_generator import AccountGenerator
                from config import BROWSER_CONFIG

                self.log_message("✓ Import modules thành công", "SUCCESS")

                # Khởi tạo managers
                proxy_manager = None
                if use_proxy:
                    proxy_manager = FastProxyManager()
                    proxy_manager.set_ui_callback(lambda msg: self.log_message(msg, "INFO"))

                account_generator = AccountGenerator()

                # Chuẩn bị proxy nhanh
                if use_proxy and proxy_manager:
                    self.log_message("🚀 Đang chuẩn bị proxy nhanh...")

                    # Load proxy đã lưu trước
                    loaded_count = proxy_manager.load_working_proxies()

                    if loaded_count < 3:  # Giảm ngưỡng xuống 3
                        self.log_message("🔍 Tìm proxy mới (tối ưu hóa)...")
                        found_count = proxy_manager.quick_fetch_and_test()

                        if found_count == 0:
                            self.log_message("⚠️ Không tìm thấy proxy! Chạy không proxy.", "WARNING")
                            use_proxy = False
                            proxy_manager = None

                    if proxy_manager:
                        proxy_info = proxy_manager.get_proxy_info()
                        self.log_message(f"✅ Sẵn sàng với {proxy_info['total']} proxy ({proxy_info['available']} khả dụng)", "SUCCESS")

                # Cập nhật cấu hình browser
                BROWSER_CONFIG['use_existing_browser'] = use_existing

                # Phân tích form
                self.log_message("Đang phân tích form đăng ký...")
                bot = RegistrationBot()
                selectors = bot.analyze_form()

                if not selectors:
                    self.log_message("Không thể phân tích form!", "ERROR")
                    return

                self.log_message("✓ Phân tích form thành công", "SUCCESS")

                # Đăng ký từng tài khoản
                successful = 0
                failed = 0

                for i in range(count):
                    if not self.is_running:
                        break

                    self.log_message(f"Đang đăng ký tài khoản #{i+1}/{count}")

                    # Lấy proxy
                    proxy = None
                    if use_proxy and proxy_manager:
                        proxy = proxy_manager.get_proxy()
                        if proxy:
                            self.log_message(f"Sử dụng proxy: {proxy['ip']}:{proxy['port']}")

                    try:
                        # Tạo bot đăng ký
                        bot = RegistrationBot(proxy)
                        success, account, error_msg = bot.register_account(selectors, manual_info)

                        # Lưu kết quả
                        bot.save_result(success, account, error_msg)

                        if success:
                            successful += 1
                            username = account.get('username', 'N/A')
                            password = account.get('password', 'N/A')
                            fullname = account.get('full_name', 'N/A')
                            bank_name = account.get('bank_name', 'N/A')

                            self.log_message(f"✓ Tài khoản #{i+1}: {username} - THÀNH CÔNG", "SUCCESS")

                            # Thêm vào bảng kết quả
                            self.add_result_to_table(i+1, username, password, fullname, bank_name, "Thành công")

                        else:
                            failed += 1
                            self.log_message(f"✗ Tài khoản #{i+1}: THẤT BẠI - {error_msg}", "ERROR")
                            self.add_result_to_table(i+1, "N/A", "N/A", "N/A", "N/A", f"Thất bại: {error_msg}")

                        # Trả lại proxy
                        if proxy and proxy_manager:
                            proxy_manager.release_proxy(proxy)

                    except Exception as e:
                        failed += 1
                        self.log_message(f"✗ Tài khoản #{i+1}: LỖI - {str(e)}", "ERROR")
                        self.add_result_to_table(i+1, "N/A", "N/A", "N/A", "N/A", f"Lỗi: {str(e)}")

                        if proxy and proxy_manager:
                            proxy_manager.release_proxy(proxy)

                    # Delay giữa các lần đăng ký
                    if i < count - 1 and self.is_running:
                        self.log_message("Chờ 3 giây trước khi đăng ký tiếp...")
                        time.sleep(3)

                # Tổng kết
                total = successful + failed
                rate = (successful / total * 100) if total > 0 else 0
                self.log_message(f"Hoàn thành! Tổng: {total}, Thành công: {successful}, Thất bại: {failed}, Tỷ lệ: {rate:.1f}%", "SUCCESS")

            except ImportError as e:
                self.log_message(f"Lỗi import modules: {e}", "ERROR")
                self.log_message("Chạy chế độ demo thay thế...", "WARNING")
                self.run_demo_mode(count, mode, manual_info)

        except Exception as e:
            self.log_message(f"Lỗi: {str(e)}", "ERROR")
        finally:
            self.is_running = False
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled'),
                self.progress.stop(),
                self.update_status("Hoàn thành")
            ])

    def test_function(self):
        """Test function"""
        self.log_message("Test function được gọi!", "INFO")
        self.log_message("Kiểm tra các module...", "INFO")

        # Test import
        try:
            sys.path.append(str(Path(__file__).parent.parent / "src"))
            from config import REGISTRATION_CONFIG
            self.log_message("✓ Import config thành công", "SUCCESS")
        except Exception as e:
            self.log_message(f"✗ Lỗi import config: {e}", "ERROR")

        try:
            from proxy_manager import ProxyManager
            self.log_message("✓ Import proxy_manager thành công", "SUCCESS")
        except Exception as e:
            self.log_message(f"✗ Lỗi import proxy_manager: {e}", "ERROR")

        self.log_message("Test hoàn thành!", "INFO")

    def add_result_to_table(self, index, username, password, fullname, bank_name, status):
        """Thêm kết quả vào bảng"""
        try:
            self.results_tree.insert('', 'end', values=(
                index, username, password, fullname, bank_name, status
            ))

            # Scroll to bottom
            children = self.results_tree.get_children()
            if children:
                self.results_tree.see(children[-1])

            # Cập nhật thông tin
            self.update_results_info()

        except Exception as e:
            self.log_message(f"Lỗi thêm kết quả: {e}", "ERROR")

    def update_results_info(self):
        """Cập nhật thông tin kết quả"""
        try:
            children = self.results_tree.get_children()
            total = len(children)

            if total == 0:
                self.results_info_label.config(text="Chưa có kết quả")
                return

            success_count = 0
            for child in children:
                values = self.results_tree.item(child)['values']
                if len(values) > 5 and "Thành công" in str(values[5]):
                    success_count += 1

            failed_count = total - success_count
            rate = (success_count / total * 100) if total > 0 else 0

            info_text = f"Tổng: {total} | Thành công: {success_count} | Thất bại: {failed_count} | Tỷ lệ: {rate:.1f}%"
            self.results_info_label.config(text=info_text)

        except Exception as e:
            self.log_message(f"Lỗi cập nhật thông tin: {e}", "ERROR")

    def run_demo_mode(self, count, mode, manual_info):
        """Chạy chế độ demo khi không import được modules"""
        self.log_message("Chạy chế độ demo...", "INFO")

        import random

        for i in range(count):
            if not self.is_running:
                break

            self.log_message(f"Demo đăng ký tài khoản #{i+1}/{count}")

            # Simulate processing time
            for j in range(5):
                if not self.is_running:
                    break
                time.sleep(0.5)

            if self.is_running:
                # Generate demo data
                if mode == "manual" and manual_info:
                    username = manual_info['username']
                    password = manual_info['password']
                    fullname = manual_info['full_name']
                else:
                    username = f"user{random.randint(1000, 9999)}"
                    password = f"pass{random.randint(100, 999)}"
                    fullname = f"Nguyễn Văn {chr(65 + random.randint(0, 25))}"

                bank_name = random.choice(["Vietcombank", "Techcombank", "BIDV", "VietinBank", "ACB"])

                # Simulate success/failure
                if random.random() > 0.2:  # 80% success rate in demo
                    self.log_message(f"✓ Tài khoản #{i+1}: {username} - THÀNH CÔNG (Demo)", "SUCCESS")
                    self.add_result_to_table(i+1, username, password, fullname, bank_name, "Thành công (Demo)")
                else:
                    self.log_message(f"✗ Tài khoản #{i+1}: THẤT BẠI (Demo)", "ERROR")
                    self.add_result_to_table(i+1, username, password, fullname, bank_name, "Thất bại (Demo)")

        if self.is_running:
            self.log_message("Demo hoàn thành!", "SUCCESS")

    def export_results(self):
        """Xuất kết quả ra file"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="Lưu kết quả",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("STT\tUsername\tPassword\tHọ tên\tNgân hàng\tTrạng thái\n")

                    for child in self.results_tree.get_children():
                        values = self.results_tree.item(child)['values']
                        f.write('\t'.join(str(v) for v in values) + '\n')

                self.log_message(f"Đã xuất kết quả ra: {filename}", "SUCCESS")

        except Exception as e:
            self.log_message(f"Lỗi xuất file: {e}", "ERROR")

    def clear_results(self):
        """Xóa kết quả"""
        try:
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            self.update_results_info()
            self.log_message("Đã xóa kết quả", "INFO")
        except Exception as e:
            self.log_message(f"Lỗi xóa kết quả: {e}", "ERROR")

    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleUI()
    app.run()
