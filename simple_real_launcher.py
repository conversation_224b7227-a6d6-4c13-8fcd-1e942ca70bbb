"""
Simple Real Launcher - Launcher đơn giản cho Real Registration Tool
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import subprocess

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
        except:
            pass

def show_simple_menu():
    """Hiển thị menu đơn giản"""
    root = tk.Tk()
    root.title("Chrome Auto Registration Tool")
    root.geometry("500x600")
    root.resizable(True, True)
    
    # Center window
    try:
        root.eval('tk::PlaceWindow . center')
    except:
        pass
    
    # Main frame
    main_frame = tk.Frame(root, padx=30, pady=30)
    main_frame.pack(fill='both', expand=True)
    
    # Title
    title_label = tk.Label(main_frame, 
                          text="🚀 Chrome Auto Registration Tool", 
                          font=('Arial', 16, 'bold'), 
                          fg='#2E86AB')
    title_label.pack(pady=10)
    
    subtitle_label = tk.Label(main_frame, 
                             text="Đăng ký tự động trên 13win16.com", 
                             font=('Arial', 12))
    subtitle_label.pack(pady=5)
    
    # Separator
    separator = tk.Frame(main_frame, height=2, bg='#E0E0E0')
    separator.pack(fill='x', pady=20)
    
    # Info
    info_text = """
✅ Tự động điền form đăng ký
✅ Hiển thị username, password đã tạo
✅ Sử dụng proxy tránh bị chặn
✅ Chụp screenshot quá trình
✅ Lưu kết quả chi tiết
    """
    
    info_label = tk.Label(main_frame, text=info_text, 
                         font=('Arial', 10), justify='left', fg='#28A745')
    info_label.pack(pady=10)
    
    # Warning
    warning_label = tk.Label(main_frame, 
                            text="⚠️ Cần Chrome và kết nối internet ổn định", 
                            font=('Arial', 9), fg='#DC3545')
    warning_label.pack(pady=5)
    
    # Buttons frame
    buttons_frame = tk.Frame(main_frame)
    buttons_frame.pack(pady=30)
    
    # Functions
    def start_real_mode():
        """Khởi động Real Mode"""
        root.destroy()
        try:
            ui_script = Path(__file__).parent / "ui" / "simple_ui.py"
            if ui_script.exists():
                subprocess.run([sys.executable, str(ui_script)])
            else:
                messagebox.showerror("Lỗi", "Không tìm thấy ui/simple_ui.py")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể khởi động: {e}")
    
    def start_demo_mode():
        """Khởi động Demo Mode"""
        root.destroy()
        try:
            demo_script = Path(__file__).parent / "demo_mode_launcher.py"
            if demo_script.exists():
                subprocess.run([sys.executable, str(demo_script)])
            else:
                messagebox.showerror("Lỗi", "Không tìm thấy demo_mode_launcher.py")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể khởi động Demo: {e}")
    
    def fix_chrome():
        """Fix ChromeDriver"""
        root.destroy()
        try:
            fix_scripts = ["fix_chrome_137.py", "simple_chrome_fix.py", "fix_chromedriver.py"]
            for script_name in fix_scripts:
                script_path = Path(__file__).parent / script_name
                if script_path.exists():
                    subprocess.run([sys.executable, str(script_path)])
                    return
            messagebox.showerror("Lỗi", "Không tìm thấy script fix ChromeDriver")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể fix Chrome: {e}")
    
    def show_help():
        """Hiển thị help"""
        help_text = """
🎯 REAL MODE:
- Đăng ký thật trên 13win16.com
- Hiển thị username, password thật
- Cần ChromeDriver hoạt động

🎮 DEMO MODE:
- Test giao diện
- Không cần ChromeDriver
- Tương thích Chrome 137

🔧 FIX CHROME:
- Sửa lỗi ChromeDriver
- Tải driver mới
- Xóa cache cũ
        """
        messagebox.showinfo("Hướng dẫn", help_text)
    
    # Buttons
    real_btn = tk.Button(buttons_frame, 
                        text="🎯 Đăng ký thật (Real Mode)", 
                        command=start_real_mode,
                        width=30, height=2,
                        font=('Arial', 11, 'bold'), 
                        bg='#28A745', fg='white')
    real_btn.pack(pady=8)
    
    demo_btn = tk.Button(buttons_frame, 
                        text="🎮 Demo Mode (Test UI)", 
                        command=start_demo_mode,
                        width=30, height=2,
                        font=('Arial', 10), 
                        bg='#FFC107', fg='black')
    demo_btn.pack(pady=5)
    
    fix_btn = tk.Button(buttons_frame, 
                       text="🔧 Fix ChromeDriver", 
                       command=fix_chrome,
                       width=30, height=2,
                       font=('Arial', 10), 
                       bg='#17A2B8', fg='white')
    fix_btn.pack(pady=5)
    
    help_btn = tk.Button(buttons_frame, 
                        text="❓ Hướng dẫn", 
                        command=show_help,
                        width=30, height=1,
                        font=('Arial', 9), 
                        bg='#6C757D', fg='white')
    help_btn.pack(pady=5)
    
    exit_btn = tk.Button(buttons_frame, 
                        text="❌ Thoát", 
                        command=root.destroy,
                        width=30, height=1,
                        font=('Arial', 9), 
                        bg='#DC3545', fg='white')
    exit_btn.pack(pady=10)
    
    # Status
    status_label = tk.Label(main_frame, 
                           text="✅ Sẵn sàng - Chọn chế độ để bắt đầu", 
                           font=('Arial', 8), fg='green')
    status_label.pack(side='bottom', pady=10)
    
    # Run
    root.mainloop()

def main():
    """Hàm main"""
    print("🚀 Chrome Auto Registration Tool - Simple Launcher")
    print("=" * 60)
    
    setup_environment()
    
    try:
        show_simple_menu()
    except Exception as e:
        print(f"❌ Lỗi launcher: {e}")
        print("\n🔄 Thử chạy trực tiếp:")
        print("python ui/simple_ui.py")
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
