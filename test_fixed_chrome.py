"""
Test Fixed Chrome - Test tool với ChromeDriver đã fix
"""

import sys
from pathlib import Path

# Thêm src vào path
sys.path.append(str(Path(__file__).parent / "src"))

def test_real_bot_with_fixed_chrome():
    """Test RealBot với ChromeDriver đã fix"""
    print("🧪 Test RealBot với ChromeDriver đã fix")
    print("=" * 60)
    
    try:
        from real_registration_bot import RealRegistrationBot
        print("✅ Import RealRegistrationBot thành công")
        
        # Callback để hiển thị log
        def log_callback(message, level):
            print(f"[{level}] {message}")
        
        # Tạo bot
        print("\n🤖 Tạo RealRegistrationBot...")
        bot = RealRegistrationBot(ui_callback=log_callback)
        print("✅ Tạo bot thành công")
        
        # Test setup Chrome driver
        print("\n🌐 Test setup Chrome driver...")
        if bot.setup_chrome_driver():
            print("✅ Setup Chrome driver THÀNH CÔNG!")
            print("✅ ChromeDriver local đã hoạt động")
            
            # Test navigate
            print("\n📱 Test navigate to register page...")
            if bot.navigate_to_register_page():
                print("✅ Navigate THÀNH CÔNG!")
                print("✅ Có thể truy cập 13win16.com")
                
                # Chụp screenshot test
                screenshot_path = bot.take_screenshot("test_success")
                if screenshot_path:
                    print(f"📸 Screenshot: {screenshot_path}")
                
                # Đóng driver
                if bot.driver:
                    bot.driver.quit()
                
                print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
                print("✅ RealBot có thể mở Chrome và truy cập website")
                print("✅ ChromeDriver local hoạt động hoàn hảo")
                return True
            else:
                print("❌ Navigate thất bại")
                if bot.driver:
                    bot.driver.quit()
                return False
        else:
            print("❌ Setup Chrome driver thất bại")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def test_chrome137_bot():
    """Test Chrome137Bot"""
    print("\n🧪 Test Chrome137Bot")
    print("=" * 60)
    
    try:
        from chrome137_registration_bot import Chrome137RegistrationBot
        print("✅ Import Chrome137RegistrationBot thành công")
        
        # Callback để hiển thị log
        def log_callback(message, level):
            print(f"[{level}] {message}")
        
        # Tạo bot
        print("\n🤖 Tạo Chrome137RegistrationBot...")
        bot = Chrome137RegistrationBot(ui_callback=log_callback)
        print("✅ Tạo bot thành công")
        
        # Test setup Chrome driver
        print("\n🌐 Test setup Chrome 137 driver...")
        if bot.setup_chrome_driver_137():
            print("✅ Setup Chrome 137 driver THÀNH CÔNG!")
            print("✅ Chrome137Bot hoạt động")
            
            # Test navigate
            print("\n📱 Test navigate to register page...")
            if bot.navigate_to_register_page():
                print("✅ Navigate THÀNH CÔNG!")
                print("✅ Chrome137Bot có thể truy cập website")
                
                # Đóng driver
                if bot.driver:
                    bot.driver.quit()
                
                print("\n🎉 CHROME137BOT TEST THÀNH CÔNG!")
                return True
            else:
                print("❌ Navigate thất bại")
                if bot.driver:
                    bot.driver.quit()
                return False
        else:
            print("❌ Setup Chrome 137 driver thất bại")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test Chrome137Bot: {e}")
        return False

def main():
    """Hàm main"""
    print("🚀 Test Tool với ChromeDriver đã fix")
    print("Kiểm tra RealBot và Chrome137Bot")
    print()
    
    # Test RealBot
    success1 = test_real_bot_with_fixed_chrome()
    
    # Test Chrome137Bot
    success2 = test_chrome137_bot()
    
    # Kết quả tổng
    print("\n" + "=" * 60)
    print("📊 KẾT QUẢ TỔNG:")
    print(f"RealBot: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Chrome137Bot: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 or success2:
        print("\n🎉 ÍT NHẤT 1 BOT HOẠT ĐỘNG!")
        print("✅ Tool sẵn sàng mở Chrome và đăng ký thật")
        print("✅ 3-Layer Fallback System đảm bảo luôn có kết quả")
        
        print("\n💡 Bây giờ có thể chạy:")
        print("   start_chrome137_mode.bat")
        print("   python ui/simple_ui.py")
        
        if success1:
            print("   🌐 RealBot sẽ mở Chrome và đăng ký thật")
        if success2:
            print("   🔧 Chrome137Bot tối ưu cho Chrome 137")
    else:
        print("\n❌ CẢ 2 BOT ĐỀU LỖI!")
        print("💡 Nhưng vẫn có SimpleBot (mô phỏng):")
        print("   demo_mode.bat")
        print("   Hoặc tick 'Chế độ nhanh' trong UI")
    
    print("=" * 60)
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
