"""
Tạ<PERSON> thông tin tài khoản ngẫu nhiên
"""

import random
import string
from faker import Faker
from config import ACCOUNT_CONFIG
import logging

class AccountGenerator:
    def __init__(self):
        self.fake = Faker(['vi_VN', 'en_US'])  # Hỗ trợ tiếng Việt và tiếng Anh
        self.logger = logging.getLogger(__name__)

    def generate_username(self, length=8):
        """Tạo username ngẫu nhiên"""
        # Kết hợp tên và số
        first_name = self.fake.first_name().lower()
        numbers = ''.join(random.choices(string.digits, k=4))
        username = f"{first_name}{numbers}"

        # Đảm bảo độ dài phù hợp
        if len(username) > length:
            username = username[:length]
        elif len(username) < length:
            username += ''.join(random.choices(string.ascii_lowercase + string.digits,
                                             k=length - len(username)))

        return username

    def generate_email(self):
        """Tạo email ngẫu nhiên"""
        username = self.generate_username(10)
        domain = random.choice(ACCOUNT_CONFIG['email_domains'])
        return f"{username}@{domain}"

    def generate_password(self):
        """Tạo mật khẩu mạnh"""
        length = ACCOUNT_CONFIG['password_length']

        # Đảm bảo có ít nhất 1 ký tự từ mỗi loại
        password = [
            random.choice(string.ascii_uppercase),  # Chữ hoa
            random.choice(string.ascii_lowercase),  # Chữ thường
            random.choice(string.digits),           # Số
            random.choice('!@#$%^&*')              # Ký tự đặc biệt
        ]

        # Thêm các ký tự ngẫu nhiên
        all_chars = string.ascii_letters + string.digits + '!@#$%^&*'
        for _ in range(length - 4):
            password.append(random.choice(all_chars))

        # Trộn ngẫu nhiên
        random.shuffle(password)
        return ''.join(password)

    def generate_phone(self):
        """Tạo số điện thoại ngẫu nhiên"""
        country_code = random.choice(ACCOUNT_CONFIG['phone_country_codes'])

        if country_code == "+84":  # Việt Nam
            # Số điện thoại Việt Nam: +84 + 9 số
            phone_number = ''.join(random.choices(string.digits, k=9))
            # Đảm bảo số đầu hợp lệ (3, 5, 7, 8, 9)
            phone_number = random.choice(['3', '5', '7', '8', '9']) + phone_number[1:]
        else:
            # Số điện thoại quốc tế: 10 số
            phone_number = ''.join(random.choices(string.digits, k=10))

        return f"{country_code}{phone_number}"

    def generate_name(self):
        """Tạo họ tên ngẫu nhiên"""
        return {
            'first_name': self.fake.first_name(),
            'last_name': self.fake.last_name(),
            'full_name': self.fake.name()
        }

    def generate_birth_date(self):
        """Tạo ngày sinh ngẫu nhiên"""
        age = random.randint(ACCOUNT_CONFIG['min_age'], ACCOUNT_CONFIG['max_age'])
        birth_date = self.fake.date_of_birth(minimum_age=age, maximum_age=age)
        return {
            'birth_date': birth_date,
            'day': birth_date.day,
            'month': birth_date.month,
            'year': birth_date.year
        }

    def generate_address(self):
        """Tạo địa chỉ ngẫu nhiên"""
        return {
            'address': self.fake.address(),
            'city': self.fake.city(),
            'country': self.fake.country(),
            'postal_code': self.fake.postcode()
        }

    def generate_account(self):
        """Tạo thông tin tài khoản đầy đủ - method chính"""
        name_info = self.generate_name()
        birth_info = self.generate_birth_date()
        address_info = self.generate_address()

        account = {
            'username': self.generate_username(),
            'email': self.generate_email(),
            'password': self.generate_password(),
            'phone': self.generate_phone(),
            'first_name': name_info['first_name'],
            'last_name': name_info['last_name'],
            'full_name': name_info['full_name'],
            'birth_day': birth_info['day'],
            'birth_month': birth_info['month'],
            'birth_year': birth_info['year'],
            'address': address_info['address'],
            'city': address_info['city'],
            'country': address_info['country'],
            'postal_code': address_info['postal_code']
        }

        self.logger.info(f"Đã tạo tài khoản: {account['username']} - {account['email']}")
        return account

    def generate_complete_account(self):
        """Alias cho generate_account() để tương thích ngược"""
        return self.generate_account()

    def save_account(self, account, filename):
        """Lưu thông tin tài khoản vào file"""
        try:
            with open(filename, 'a', encoding='utf-8') as f:
                account_line = (
                    f"Username: {account['username']} | "
                    f"Email: {account['email']} | "
                    f"Password: {account['password']} | "
                    f"Phone: {account['phone']} | "
                    f"Name: {account['full_name']}\n"
                )
                f.write(account_line)
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu tài khoản: {e}")

# Test function
if __name__ == "__main__":
    generator = AccountGenerator()
    account = generator.generate_complete_account()
    print("Thông tin tài khoản mẫu:")
    for key, value in account.items():
        print(f"{key}: {value}")
