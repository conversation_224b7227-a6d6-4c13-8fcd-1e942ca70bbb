"""
Quick Launcher - Launcher nhanh cho Chrome Auto Registration Tool
"""

import sys
import os
from pathlib import Path
import subprocess

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
        except:
            pass

def show_menu():
    """Hiển thị menu"""
    while True:
        print("\n" + "=" * 60)
        print("🚀 Chrome Auto Registration Tool - Quick Launcher")
        print("=" * 60)
        print()
        print("Chọn chế độ:")
        print("1. 🎯 Real Mode - Đăng ký thật trên 13win16.com")
        print("2. 🎮 Demo Mode - Test UI (Chrome 137 compatible)")
        print("3. 🔧 Fix ChromeDriver - Sửa lỗi driver")
        print("4. 📖 Hướng dẫn")
        print("5. ❌ Thoát")
        print()
        
        try:
            choice = input("Nhập lựa chọn (1-5): ").strip()
            
            if choice == "1":
                start_real_mode()
                break
            elif choice == "2":
                start_demo_mode()
                break
            elif choice == "3":
                fix_chrome()
                break
            elif choice == "4":
                show_help()
            elif choice == "5":
                print("👋 Tạm biệt!")
                break
            else:
                print("❌ Lựa chọn không hợp lệ!")
                
        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")

def start_real_mode():
    """Khởi động Real Mode"""
    print("\n🎯 Đang khởi động Real Mode...")
    print("✅ Đăng ký thật trên 13win16.com")
    print("✅ Hiển thị username, password đã tạo")
    
    try:
        ui_script = Path(__file__).parent / "ui" / "simple_ui.py"
        if ui_script.exists():
            print("🚀 Khởi động UI...")
            subprocess.run([sys.executable, str(ui_script)])
        else:
            print("❌ Không tìm thấy ui/simple_ui.py")
            print("💡 Thử chạy: python ui/simple_ui.py")
            input("Nhấn Enter để tiếp tục...")
    except Exception as e:
        print(f"❌ Lỗi khởi động Real Mode: {e}")
        input("Nhấn Enter để tiếp tục...")

def start_demo_mode():
    """Khởi động Demo Mode"""
    print("\n🎮 Đang khởi động Demo Mode...")
    print("✅ Tương thích Chrome 137")
    print("✅ Không cần ChromeDriver")
    
    try:
        demo_script = Path(__file__).parent / "demo_mode_launcher.py"
        if demo_script.exists():
            print("🚀 Khởi động Demo...")
            subprocess.run([sys.executable, str(demo_script)])
        else:
            print("❌ Không tìm thấy demo_mode_launcher.py")
            print("💡 Thử chạy: python demo_mode_launcher.py")
            input("Nhấn Enter để tiếp tục...")
    except Exception as e:
        print(f"❌ Lỗi khởi động Demo Mode: {e}")
        input("Nhấn Enter để tiếp tục...")

def fix_chrome():
    """Fix ChromeDriver"""
    print("\n🔧 Đang fix ChromeDriver...")
    
    try:
        fix_scripts = [
            ("fix_chrome_137.py", "Fix đặc biệt cho Chrome 137"),
            ("simple_chrome_fix.py", "Fix đơn giản"),
            ("fix_chromedriver.py", "Fix tổng quát")
        ]
        
        for script_name, description in fix_scripts:
            script_path = Path(__file__).parent / script_name
            if script_path.exists():
                print(f"🚀 {description}: {script_name}")
                subprocess.run([sys.executable, str(script_path)])
                return
        
        print("❌ Không tìm thấy script fix ChromeDriver")
        print("💡 Thử tải ChromeDriver thủ công")
        input("Nhấn Enter để tiếp tục...")
        
    except Exception as e:
        print(f"❌ Lỗi fix Chrome: {e}")
        input("Nhấn Enter để tiếp tục...")

def show_help():
    """Hiển thị hướng dẫn"""
    print("\n" + "=" * 60)
    print("📖 HƯỚNG DẪN SỬ DỤNG")
    print("=" * 60)
    print()
    print("🎯 REAL MODE:")
    print("   • Đăng ký thật trên 13win16.com")
    print("   • Hiển thị username, password thật đã tạo")
    print("   • Cần ChromeDriver hoạt động")
    print("   • Sử dụng proxy tránh bị chặn")
    print("   • Chụp screenshot quá trình")
    print("   • Lưu kết quả vào file")
    print()
    print("🎮 DEMO MODE:")
    print("   • Test giao diện tool")
    print("   • Tương thích Chrome 137")
    print("   • Không cần ChromeDriver")
    print("   • Hiển thị UI đầy đủ")
    print("   • Xuất file demo")
    print()
    print("🔧 FIX CHROMEDRIVER:")
    print("   • Sửa lỗi ChromeDriver")
    print("   • Tải driver mới")
    print("   • Xóa cache cũ")
    print("   • Tương thích Chrome 137")
    print()
    print("💡 KHUYẾN NGHỊ:")
    print("   • Chrome 137: Sử dụng Demo Mode")
    print("   • Chrome cũ hơn: Sử dụng Real Mode")
    print("   • Gặp lỗi: Chạy Fix ChromeDriver")
    print("   • Lần đầu: Thử Demo Mode trước")
    print()
    print("📁 FILES QUAN TRỌNG:")
    print("   • start_tool.bat - Launcher GUI")
    print("   • demo_mode.bat - Demo Mode nhanh")
    print("   • start_real_registration.bat - Real Mode")
    print("   • fix_chrome_137.bat - Fix Chrome 137")
    print()
    input("Nhấn Enter để tiếp tục...")

def check_files():
    """Kiểm tra files quan trọng"""
    print("\n🔍 Kiểm tra files...")
    
    important_files = [
        ("ui/simple_ui.py", "Real Mode UI"),
        ("demo_mode_launcher.py", "Demo Mode"),
        ("fix_chrome_137.py", "Chrome 137 Fix"),
        ("src/config.py", "Configuration"),
        ("requirements.txt", "Dependencies")
    ]
    
    missing_files = []
    
    for file_path, description in important_files:
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} - THIẾU")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Thiếu {len(missing_files)} files quan trọng!")
        print("💡 Vui lòng tải lại tool hoặc kiểm tra cấu trúc thư mục")
    else:
        print("\n✅ Tất cả files quan trọng đều có!")
    
    input("Nhấn Enter để tiếp tục...")

def main():
    """Hàm main"""
    setup_environment()
    
    print("🚀 Chrome Auto Registration Tool - Quick Launcher")
    print("Launcher nhanh với đầy đủ tính năng")
    
    # Kiểm tra files cơ bản
    ui_file = Path(__file__).parent / "ui" / "simple_ui.py"
    demo_file = Path(__file__).parent / "demo_mode_launcher.py"
    
    if not ui_file.exists() and not demo_file.exists():
        print("\n❌ Không tìm thấy files cần thiết!")
        print("💡 Vui lòng kiểm tra cấu trúc thư mục")
        input("Nhấn Enter để thoát...")
        return
    
    try:
        show_menu()
    except Exception as e:
        print(f"❌ Lỗi launcher: {e}")
        print("\n🔄 Thử chạy trực tiếp:")
        print("python ui/simple_ui.py")
        print("python demo_mode_launcher.py")
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
