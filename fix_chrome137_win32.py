"""
Fix Chrome 137 Win32 Error - Sửa lỗi ChromeDriver Win32
"""

import os
import sys
import shutil
import requests
import zipfile
from pathlib import Path
import subprocess

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
        except:
            pass

def get_chrome_version():
    """Lấy version Chrome hiện tại"""
    try:
        # Thử các cách khác nhau để lấy Chrome version
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                try:
                    result = subprocess.run([chrome_path, '--version'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version_line = result.stdout.strip()
                        # Extract version number
                        version = version_line.split()[-1]
                        print(f"✅ Tìm thấy Chrome version: {version}")
                        return version
                except Exception as e:
                    print(f"⚠️ Lỗi lấy version từ {chrome_path}: {e}")
                    continue
        
        print("⚠️ Không thể lấy Chrome version, sử dụng 137.0.7151.55")
        return "137.0.7151.55"
        
    except Exception as e:
        print(f"⚠️ Lỗi lấy Chrome version: {e}")
        return "137.0.7151.55"

def clean_old_chromedriver():
    """Xóa ChromeDriver cũ"""
    try:
        print("🧹 Dọn dẹp ChromeDriver cũ...")
        
        # Xóa cache WebDriverManager
        wdm_cache = Path.home() / ".wdm"
        if wdm_cache.exists():
            shutil.rmtree(wdm_cache)
            print("✅ Đã xóa cache WebDriverManager")
        
        # Xóa chromedriver trong PATH
        chromedriver_paths = [
            "chromedriver.exe",
            r"C:\Windows\chromedriver.exe",
            r"C:\Windows\System32\chromedriver.exe"
        ]
        
        for path in chromedriver_paths:
            if os.path.exists(path):
                try:
                    os.remove(path)
                    print(f"✅ Đã xóa {path}")
                except Exception as e:
                    print(f"⚠️ Không thể xóa {path}: {e}")
        
        print("✅ Dọn dẹp hoàn thành")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi dọn dẹp: {e}")
        return False

def download_chromedriver_manual(version):
    """Tải ChromeDriver thủ công"""
    try:
        print(f"📥 Tải ChromeDriver {version} thủ công...")
        
        # URL ChromeDriver mới
        major_version = version.split('.')[0]
        
        # Thử các URL khác nhau
        urls = [
            f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win64/chromedriver-win64.zip",
            f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win32/chromedriver-win32.zip",
            f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
        ]
        
        download_dir = Path(__file__).parent / "chromedriver_temp"
        download_dir.mkdir(exist_ok=True)
        
        for url in urls:
            try:
                print(f"🔄 Thử tải từ: {url}")
                
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    zip_path = download_dir / "chromedriver.zip"
                    
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    print("✅ Tải thành công, đang giải nén...")
                    
                    # Giải nén
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(download_dir)
                    
                    # Tìm chromedriver.exe
                    chromedriver_exe = None
                    for root, dirs, files in os.walk(download_dir):
                        for file in files:
                            if file == "chromedriver.exe":
                                chromedriver_exe = Path(root) / file
                                break
                        if chromedriver_exe:
                            break
                    
                    if chromedriver_exe and chromedriver_exe.exists():
                        # Copy vào thư mục hiện tại
                        target_path = Path(__file__).parent / "chromedriver.exe"
                        shutil.copy2(chromedriver_exe, target_path)
                        
                        # Set executable permission
                        os.chmod(target_path, 0o755)
                        
                        print(f"✅ ChromeDriver đã được cài đặt: {target_path}")
                        
                        # Dọn dẹp
                        shutil.rmtree(download_dir)
                        
                        return str(target_path)
                    else:
                        print("❌ Không tìm thấy chromedriver.exe trong file zip")
                        
                else:
                    print(f"⚠️ URL không khả dụng: {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ Lỗi tải từ {url}: {e}")
                continue
        
        print("❌ Không thể tải ChromeDriver từ tất cả URL")
        return None
        
    except Exception as e:
        print(f"❌ Lỗi tải ChromeDriver: {e}")
        return None

def test_chromedriver(chromedriver_path):
    """Test ChromeDriver"""
    try:
        print(f"🧪 Test ChromeDriver: {chromedriver_path}")
        
        if not os.path.exists(chromedriver_path):
            print("❌ ChromeDriver không tồn tại")
            return False
        
        # Test version
        result = subprocess.run([chromedriver_path, '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print(f"✅ ChromeDriver hoạt động: {version_info}")
            return True
        else:
            print(f"❌ ChromeDriver lỗi: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test ChromeDriver: {e}")
        return False

def create_simple_test():
    """Tạo test script đơn giản"""
    try:
        test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

def test_chrome137():
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        print("🧪 Test Chrome 137 với ChromeDriver mới...")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Sử dụng ChromeDriver local
        chromedriver_path = Path(__file__).parent / "chromedriver.exe"
        if chromedriver_path.exists():
            service = Service(str(chromedriver_path))
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Test thành công! Title: {title}")
        return True
        
    except Exception as e:
        print(f"❌ Test thất bại: {e}")
        return False

if __name__ == "__main__":
    test_chrome137()
    input("Nhấn Enter để thoát...")
"""
        
        test_file = Path(__file__).parent / "test_chrome137_fix.py"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print(f"✅ Đã tạo test script: {test_file}")
        return str(test_file)
        
    except Exception as e:
        print(f"❌ Lỗi tạo test script: {e}")
        return None

def main():
    """Hàm main"""
    setup_environment()
    
    print("🔧 Fix Chrome 137 Win32 Error")
    print("=" * 60)
    print("Sửa lỗi: [WinError 193] %1 is not a valid Win32 application")
    print("=" * 60)
    
    # Bước 1: Lấy Chrome version
    chrome_version = get_chrome_version()
    
    # Bước 2: Dọn dẹp ChromeDriver cũ
    clean_old_chromedriver()
    
    # Bước 3: Tải ChromeDriver mới
    chromedriver_path = download_chromedriver_manual(chrome_version)
    
    if chromedriver_path:
        # Bước 4: Test ChromeDriver
        if test_chromedriver(chromedriver_path):
            print("\n🎉 FIX THÀNH CÔNG!")
            print("✅ ChromeDriver đã được cài đặt và hoạt động")
            print(f"📁 Vị trí: {chromedriver_path}")
            
            # Tạo test script
            test_file = create_simple_test()
            if test_file:
                print(f"🧪 Test script: {test_file}")
            
            print("\n💡 Bây giờ có thể chạy:")
            print("   python ui/simple_ui.py")
            print("   start_tool.bat")
            
        else:
            print("\n❌ ChromeDriver vẫn không hoạt động")
            print("💡 Thử:")
            print("   1. Cập nhật Chrome lên version mới nhất")
            print("   2. Chạy demo_mode.bat (không cần ChromeDriver)")
    else:
        print("\n❌ Không thể tải ChromeDriver")
        print("💡 Giải pháp thay thế:")
        print("   1. Chạy demo_mode.bat")
        print("   2. Tick 'Chế độ nhanh' trong UI")
        print("   3. Cập nhật Chrome và thử lại")
    
    print("\n" + "=" * 60)
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
