"""
Setup script cho Auto Registration Tool
"""

import subprocess
import sys
import os

def install_requirements():
    """Cài đặt dependencies"""
    print("🔧 Đang cài đặt dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Cài đặt dependencies thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi cài đặt dependencies: {e}")
        return False

def create_directories():
    """Tạo thư mục cần thiết"""
    directories = ["screenshots", "logs", "output"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Đã tạo thư mục: {directory}")

def check_python_version():
    """Kiểm tra phiên bản Python"""
    if sys.version_info < (3, 7):
        print("❌ Cần Python 3.7 trở lên!")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} - OK")
    return True

def main():
    """Setup chính"""
    print("🚀 SETUP AUTO REGISTRATION TOOL")
    print("=" * 40)
    
    # Kiểm tra Python version
    if not check_python_version():
        return
    
    # Tạo thư mục
    create_directories()
    
    # Cài đặt dependencies
    if not install_requirements():
        return
    
    print("\n" + "=" * 40)
    print("🎉 SETUP HOÀN THÀNH!")
    print("\nCách sử dụng:")
    print("1. Chạy test: python test_tool.py")
    print("2. Chạy tool: python main.py")
    print("3. Hoặc dùng: run_tool.bat (Windows)")

if __name__ == "__main__":
    main()
