"""
Real Registration Launcher - Khởi động tool đăng ký thật trên 13win16.com
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import subprocess

def setup_environment():
    """Thiết lập môi trường"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def check_dependencies():
    """Kiểm tra dependencies"""
    required_packages = [
        'selenium',
        'webdriver-manager',
        'requests',
        'fake-useragent',
        'faker'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == 'fake-useragent':
                __import__('fake_useragent')
            elif package == 'webdriver-manager':
                __import__('webdriver_manager')
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        root = tk.Tk()
        root.withdraw()

        message = f"""Tool cần các package sau để đăng ký thật:
{', '.join(missing_packages)}

Bạn có muốn cài đặt tự động không?
(Sẽ chạy: pip install -r requirements.txt)"""

        result = messagebox.askyesno("Cần cài đặt Dependencies", message)
        root.destroy()

        if result:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages,
                             check=True)
                messagebox.showinfo("Thành công", "Đã cài đặt thành công! Vui lòng khởi động lại.")
                return False
            except subprocess.CalledProcessError:
                messagebox.showerror("Lỗi", "Không thể cài đặt dependencies. Vui lòng cài đặt thủ công.")
                return False
        else:
            return False

    return True

def check_chrome():
    """Kiểm tra Chrome"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    ]

    for chrome_path in chrome_paths:
        if os.path.exists(chrome_path):
            return True

    root = tk.Tk()
    root.withdraw()
    messagebox.showerror("Lỗi", "Không tìm thấy Google Chrome!\nVui lòng cài đặt Chrome trước.")
    root.destroy()
    return False

def show_startup_menu():
    """Hiển thị menu khởi động"""
    root = tk.Tk()
    root.title("Chrome Auto Registration Tool - Real Mode")
    root.geometry("700x800")
    root.resizable(True, True)

    # Center window
    root.eval('tk::PlaceWindow . center')

    # Header
    header_frame = tk.Frame(root, bg='#2E86AB', height=100)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)

    title_label = tk.Label(header_frame, text="🚀 Chrome Auto Registration Tool",
                          font=('Arial', 16, 'bold'), fg='white', bg='#2E86AB')
    title_label.pack(expand=True)

    subtitle_label = tk.Label(header_frame, text="Real Mode - Đăng ký thật trên 13win16.com",
                             font=('Arial', 12), fg='white', bg='#2E86AB')
    subtitle_label.pack()

    # Main content
    content_frame = tk.Frame(root, padx=20, pady=20)
    content_frame.pack(fill='both', expand=True)

    # Info
    info_label = tk.Label(content_frame,
                         text="Tool sẽ tự động đăng ký tài khoản thật trên 13win16.com",
                         font=('Arial', 12, 'bold'))
    info_label.pack(pady=5)

    # Features
    features_text = """
✅ Tự động điền form đăng ký
✅ Sử dụng proxy để tránh bị chặn
✅ Tạo thông tin tài khoản ngẫu nhiên
✅ Hoặc nhập thông tin thủ công
✅ Chụp screenshot quá trình
✅ Lưu kết quả chi tiết
✅ Hiển thị username, password đã tạo
    """

    features_label = tk.Label(content_frame, text=features_text,
                             font=('Arial', 10), justify='left', fg='#28A745')
    features_label.pack(pady=5)

    # Warning
    warning_text = """
⚠️ LƯU Ý QUAN TRỌNG:
• Tool sẽ mở Chrome và tự động điền form
• Cần kết nối internet ổn định
• Có thể cần giải captcha thủ công
• Tuân thủ điều khoản của website
    """

    warning_label = tk.Label(content_frame, text=warning_text,
                            font=('Arial', 9), justify='left', fg='#DC3545')
    warning_label.pack(pady=5)

    # Buttons - đặt ở cuối và đảm bảo hiển thị
    button_frame = tk.Frame(content_frame)
    button_frame.pack(side='bottom', fill='x', pady=20)

    def start_real_mode():
        root.destroy()
        launch_real_ui()

    def start_demo_mode():
        root.destroy()
        launch_demo_mode()

    def start_fix_chrome():
        root.destroy()
        launch_chrome_fix()

    # Real mode button
    real_btn = tk.Button(button_frame, text="🎯 Bắt đầu đăng ký thật",
                        command=start_real_mode, width=25, height=3,
                        font=('Arial', 12, 'bold'), bg='#28A745', fg='white')
    real_btn.pack(pady=5)

    # Demo mode button
    demo_btn = tk.Button(button_frame, text="🎮 Demo Mode (Test UI)",
                        command=start_demo_mode, width=25, height=2,
                        font=('Arial', 10), bg='#FFC107', fg='black')
    demo_btn.pack(pady=5)

    # Fix Chrome button
    fix_btn = tk.Button(button_frame, text="🔧 Fix ChromeDriver",
                       command=start_fix_chrome, width=25, height=2,
                       font=('Arial', 10), bg='#17A2B8', fg='white')
    fix_btn.pack(pady=5)

    # Exit button
    exit_btn = tk.Button(button_frame, text="❌ Thoát",
                        command=root.destroy, width=25, height=2,
                        font=('Arial', 10), bg='#DC3545', fg='white')
    exit_btn.pack(pady=10)

    root.mainloop()

def launch_real_ui():
    """Khởi động Real UI"""
    try:
        print("🎯 Đang khởi động Real Registration UI...")

        ui_script = Path(__file__).parent / "ui" / "simple_ui.py"

        if ui_script.exists():
            subprocess.run([sys.executable, str(ui_script)])
        else:
            print("❌ Không tìm thấy ui/simple_ui.py")
            input("Nhấn Enter để thoát...")

    except Exception as e:
        print(f"❌ Lỗi khởi động Real UI: {e}")
        input("Nhấn Enter để thoát...")

def launch_demo_mode():
    """Khởi động Demo Mode"""
    try:
        print("🎮 Đang khởi động Demo Mode...")

        demo_script = Path(__file__).parent / "demo_mode_launcher.py"

        if demo_script.exists():
            subprocess.run([sys.executable, str(demo_script)])
        else:
            print("❌ Không tìm thấy demo_mode_launcher.py")
            input("Nhấn Enter để thoát...")

    except Exception as e:
        print(f"❌ Lỗi khởi động Demo Mode: {e}")
        input("Nhấn Enter để thoát...")

def launch_chrome_fix():
    """Khởi động Chrome Fix"""
    try:
        print("🔧 Đang khởi động Chrome Fix...")

        # Thử các script fix khác nhau
        fix_scripts = [
            "fix_chrome_137.py",
            "simple_chrome_fix.py",
            "fix_chromedriver.py"
        ]

        for script_name in fix_scripts:
            script_path = Path(__file__).parent / script_name
            if script_path.exists():
                subprocess.run([sys.executable, str(script_path)])
                return

        print("❌ Không tìm thấy script fix ChromeDriver")
        input("Nhấn Enter để thoát...")

    except Exception as e:
        print(f"❌ Lỗi khởi động Chrome Fix: {e}")
        input("Nhấn Enter để thoát...")

def main():
    """Hàm main"""
    print("🚀 Chrome Auto Registration Tool - Real Mode Launcher")
    print("=" * 70)

    # Thiết lập môi trường
    setup_environment()

    # Kiểm tra Chrome
    if not check_chrome():
        input("Nhấn Enter để thoát...")
        return

    # Kiểm tra dependencies
    if not check_dependencies():
        input("Nhấn Enter để thoát...")
        return

    print("✅ Tất cả dependencies đã sẵn sàng")
    print("🎯 Khởi động menu chọn chế độ...")

    # Hiển thị menu
    try:
        show_startup_menu()
    except Exception as e:
        print(f"❌ Lỗi launcher: {e}")
        print("\n🔄 Thử chạy trực tiếp:")
        print("python ui/simple_ui.py")
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
