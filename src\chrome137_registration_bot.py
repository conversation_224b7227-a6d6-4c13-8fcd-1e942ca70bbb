"""
Chrome 137 Registration Bot - Tối ưu cho Chrome 137
"""

import time
import random
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from pathlib import Path
from datetime import datetime

from config import REGISTER_URL, BROWSER_CONFIG, DELAY_CONFIG, OUTPUT_CONFIG
from account_generator import AccountGenerator

class Chrome137RegistrationBot:
    def __init__(self, proxy=None, ui_callback=None):
        self.proxy = proxy
        self.ui_callback = ui_callback
        self.driver = None
        self.wait = None
        self.account_generator = AccountGenerator()
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Thiết lập logging"""
        try:
            log_file = Path(__file__).parent.parent / "data" / "logs" / "registration.log"
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_file, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )
            self.logger = logging.getLogger(__name__)
        except Exception as e:
            print(f"Lỗi setup logging: {e}")
            self.logger = logging.getLogger(__name__)

    def log(self, message, level="INFO"):
        """Ghi log và gửi về UI"""
        try:
            if level == "INFO":
                self.logger.info(message)
            elif level == "ERROR":
                self.logger.error(message)
            elif level == "WARNING":
                self.logger.warning(message)
            elif level == "SUCCESS":
                self.logger.info(f"SUCCESS: {message}")
            
            if self.ui_callback:
                self.ui_callback(message, level)
        except Exception as e:
            print(f"Lỗi log: {e}")

    def setup_chrome_driver_137(self):
        """Thiết lập Chrome driver cho Chrome 137"""
        try:
            chrome_options = Options()
            
            # Chrome 137 compatible options
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")
            chrome_options.add_argument("--disable-javascript")
            
            # Window size
            chrome_options.add_argument("--window-size=1366,768")
            
            # User agent for Chrome 137
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            
            # Proxy
            if self.proxy:
                proxy_string = f"{self.proxy['ip']}:{self.proxy['port']}"
                chrome_options.add_argument(f"--proxy-server=http://{proxy_string}")
                self.log(f"Sử dụng proxy: {proxy_string}")
            
            # Tạo Chrome mới (không kết nối existing)
            self.log("Tạo Chrome mới cho Chrome 137...")
            
            try:
                # Thử dùng ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.log(f"ChromeDriverManager lỗi: {e}", "WARNING")
                # Thử không dùng service
                self.driver = webdriver.Chrome(options=chrome_options)
            
            # Cấu hình driver
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            self.wait = WebDriverWait(self.driver, 15)
            
            self.log("Chrome 137 driver đã sẵn sàng")
            return True
            
        except Exception as e:
            self.log(f"Lỗi khi thiết lập Chrome 137 driver: {e}", "ERROR")
            return False

    def navigate_to_register_page(self):
        """Điều hướng đến trang đăng ký"""
        try:
            self.log(f"Đang truy cập: {REGISTER_URL}")
            self.driver.get(REGISTER_URL)
            
            # Chờ trang load
            time.sleep(5)
            
            # Kiểm tra trang đã load
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            self.log(f"URL hiện tại: {current_url}")
            self.log(f"Title: {page_title}")
            
            if "13win" in page_title.lower() or "register" in current_url.lower():
                self.log("Đã truy cập trang đăng ký thành công")
                return True
            else:
                self.log("Trang đã load, tiếp tục thử đăng ký")
                return True
                
        except Exception as e:
            self.log(f"Lỗi khi truy cập trang đăng ký: {e}", "ERROR")
            return False

    def find_and_fill_form(self, account_data):
        """Tìm và điền form đăng ký"""
        try:
            self.log("Bắt đầu tìm và điền form đăng ký...")
            
            # Chờ form xuất hiện
            time.sleep(3)
            
            # Tìm các trường input
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            self.log(f"Tìm thấy {len(inputs)} input fields")
            
            # Điền thông tin vào các trường
            filled_count = 0
            
            for i, input_field in enumerate(inputs):
                try:
                    input_type = input_field.get_attribute("type")
                    input_name = input_field.get_attribute("name")
                    input_placeholder = input_field.get_attribute("placeholder")
                    
                    self.log(f"Input {i}: type={input_type}, name={input_name}, placeholder={input_placeholder}")
                    
                    # Điền username/phone
                    if any(keyword in str(input_placeholder).lower() for keyword in ["phone", "số điện thoại", "username", "tên đăng nhập"]):
                        input_field.clear()
                        input_field.send_keys(account_data['username'])
                        self.log(f"Đã điền username: {account_data['username']}")
                        filled_count += 1
                        time.sleep(1)
                    
                    # Điền password
                    elif input_type == "password":
                        input_field.clear()
                        input_field.send_keys(account_data['password'])
                        self.log("Đã điền password")
                        filled_count += 1
                        time.sleep(1)
                    
                    # Điền họ tên
                    elif any(keyword in str(input_placeholder).lower() for keyword in ["name", "họ tên", "tên thật"]):
                        input_field.clear()
                        input_field.send_keys(account_data['full_name'])
                        self.log(f"Đã điền họ tên: {account_data['full_name']}")
                        filled_count += 1
                        time.sleep(1)
                    
                    # Điền email
                    elif input_type == "email" or "email" in str(input_placeholder).lower():
                        input_field.clear()
                        input_field.send_keys(account_data['email'])
                        self.log(f"Đã điền email: {account_data['email']}")
                        filled_count += 1
                        time.sleep(1)
                
                except Exception as e:
                    self.log(f"Lỗi điền input {i}: {e}", "WARNING")
                    continue
            
            self.log(f"Đã điền {filled_count} trường thông tin")
            
            # Tìm và tick checkbox
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if not checkbox.is_selected():
                        checkbox.click()
                        self.log("Đã tick checkbox")
                        time.sleep(1)
            except Exception as e:
                self.log(f"Lỗi tick checkbox: {e}", "WARNING")
            
            return filled_count > 0
            
        except Exception as e:
            self.log(f"Lỗi khi điền form: {e}", "ERROR")
            return False

    def submit_form(self):
        """Submit form đăng ký"""
        try:
            self.log("Đang tìm nút submit...")
            
            # Tìm nút submit
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            submit_button = None
            
            for button in buttons:
                button_text = button.text.lower()
                if any(keyword in button_text for keyword in ["đăng ký", "register", "submit", "gửi"]):
                    submit_button = button
                    break
            
            if submit_button:
                # Scroll đến button
                self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                time.sleep(2)
                
                # Click submit
                submit_button.click()
                self.log("Đã click nút đăng ký")
                
                # Chờ xử lý
                time.sleep(5)
                return True
            else:
                self.log("Không tìm thấy nút submit", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"Lỗi khi submit form: {e}", "ERROR")
            return False

    def check_result(self):
        """Kiểm tra kết quả đăng ký"""
        try:
            self.log("Đang kiểm tra kết quả...")
            
            # Chờ response
            time.sleep(3)
            
            current_url = self.driver.current_url
            page_source = self.driver.page_source.lower()
            
            self.log(f"URL sau submit: {current_url}")
            
            # Kiểm tra thành công
            success_indicators = ["thành công", "success", "welcome", "chào mừng"]
            error_indicators = ["lỗi", "error", "failed", "thất bại"]
            
            for indicator in success_indicators:
                if indicator in page_source:
                    return True, f"Đăng ký thành công ({indicator})"
            
            for indicator in error_indicators:
                if indicator in page_source:
                    return False, f"Đăng ký thất bại ({indicator})"
            
            # Kiểm tra URL thay đổi
            if current_url != REGISTER_URL:
                return True, "Đăng ký thành công (URL changed)"
            
            return False, "Không xác định được kết quả"
            
        except Exception as e:
            self.log(f"Lỗi kiểm tra kết quả: {e}", "ERROR")
            return False, f"Lỗi kiểm tra: {str(e)}"

    def register_account(self, manual_info=None):
        """Đăng ký một tài khoản với Chrome 137"""
        try:
            # Tạo thông tin tài khoản
            if manual_info:
                account_data = manual_info
            else:
                account_data = self.account_generator.generate_account()
            
            self.log(f"Bắt đầu đăng ký với Chrome 137: {account_data['username']}")
            
            # Thiết lập driver
            if not self.setup_chrome_driver_137():
                return False, account_data, "Không thể thiết lập Chrome 137 driver"
            
            # Truy cập trang đăng ký
            if not self.navigate_to_register_page():
                return False, account_data, "Không thể truy cập trang đăng ký"
            
            # Điền form
            if not self.find_and_fill_form(account_data):
                return False, account_data, "Không thể điền form đăng ký"
            
            # Submit form
            if not self.submit_form():
                return False, account_data, "Không thể submit form"
            
            # Kiểm tra kết quả
            success, message = self.check_result()
            
            if success:
                self.log(f"Đăng ký thành công với Chrome 137: {account_data['username']}", "SUCCESS")
                return True, account_data, message
            else:
                self.log(f"Đăng ký thất bại: {message}", "ERROR")
                return False, account_data, message
                
        except Exception as e:
            self.log(f"Lỗi trong quá trình đăng ký: {e}", "ERROR")
            return False, account_data if 'account_data' in locals() else {}, str(e)
        
        finally:
            # Đóng driver
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass

    def save_result(self, success, account_data, message):
        """Lưu kết quả đăng ký"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data_dir = Path(__file__).parent.parent / "data" / "accounts"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            if success:
                success_file = data_dir / "successful_accounts.txt"
                with open(success_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {account_data.get('password', 'N/A')} | {account_data.get('full_name', 'N/A')} | 13win16.com | {message}\n")
            else:
                failed_file = data_dir / "failed_accounts.txt"
                with open(failed_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {message}\n")
                    
        except Exception as e:
            self.log(f"Lỗi lưu kết quả: {e}", "ERROR")

# Test function
if __name__ == "__main__":
    def test_callback(message, level):
        print(f"[{level}] {message}")
    
    print("🧪 Test Chrome 137 Registration Bot")
    print("=" * 50)
    
    bot = Chrome137RegistrationBot(ui_callback=test_callback)
    success, account, message = bot.register_account()
    
    print(f"Kết quả: {success}")
    print(f"Tài khoản: {account.get('username', 'N/A')}")
    print(f"Thông báo: {message}")
    
    bot.save_result(success, account, message)
    input("Nhấn Enter để thoát...")
