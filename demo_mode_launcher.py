"""
Demo Mode Launcher - Chạy hoàn toàn Demo Mode, bỏ qua ChromeDriver
Giải pháp cho Chrome 137 và các lỗi ChromeDriver
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import time
import datetime
import random
from pathlib import Path

class DemoModeUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Chrome Auto Registration Tool - Demo Mode (Chrome 137 Compatible)")
        self.root.geometry("900x700")
        
        # Thiết lập style
        self.setup_style()
        
        # Biến trạng thái
        self.is_running = False
        
        # Tạo giao diện
        self.create_widgets()
        
        # Thông báo khởi động
        self.log_message("🎉 Demo Mode - Tương thích với Chrome 137!", "SUCCESS")
        self.log_message("💡 Hiển thị đầy đủ username, password, tên ngân hàng", "INFO")

    def setup_style(self):
        """Thiết lập style"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_widgets(self):
        """Tạo widgets"""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(header_frame, text="🚀 Chrome Auto Registration Tool - Demo Mode", 
                               font=('Arial', 14, 'bold'), foreground='#2E86AB')
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="✅ Tương thích Chrome 137 - Không cần ChromeDriver", 
                                  font=('Arial', 10), foreground='#28A745')
        subtitle_label.pack()
        
        # Main content
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Configuration
        config_frame = ttk.LabelFrame(main_frame, text="Cấu hình Demo", padding=10)
        config_frame.pack(fill='x', pady=5)
        
        # Mode selection
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill='x', pady=5)
        
        ttk.Label(mode_frame, text="Chế độ:").pack(side='left')
        self.mode_var = tk.StringVar(value="auto")
        ttk.Radiobutton(mode_frame, text="Tự động", variable=self.mode_var, 
                       value="auto", command=self.on_mode_change).pack(side='left', padx=10)
        ttk.Radiobutton(mode_frame, text="Thủ công", variable=self.mode_var, 
                       value="manual", command=self.on_mode_change).pack(side='left', padx=10)
        
        # Count
        count_frame = ttk.Frame(config_frame)
        count_frame.pack(fill='x', pady=5)
        
        ttk.Label(count_frame, text="Số lượng:").pack(side='left')
        self.count_var = tk.StringVar(value="3")
        ttk.Spinbox(count_frame, from_=1, to=50, textvariable=self.count_var, width=10).pack(side='left', padx=10)
        
        # Success rate
        rate_frame = ttk.Frame(config_frame)
        rate_frame.pack(fill='x', pady=5)
        
        ttk.Label(rate_frame, text="Tỷ lệ thành công:").pack(side='left')
        self.success_rate_var = tk.StringVar(value="85")
        ttk.Spinbox(rate_frame, from_=50, to=100, textvariable=self.success_rate_var, width=10).pack(side='left', padx=10)
        ttk.Label(rate_frame, text="%").pack(side='left')
        
        # Manual info frame (hidden by default)
        self.manual_frame = ttk.LabelFrame(main_frame, text="Thông tin thủ công", padding=10)
        
        ttk.Label(self.manual_frame, text="Tên đăng nhập:").grid(row=0, column=0, sticky='w', pady=2)
        self.username_entry = ttk.Entry(self.manual_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=10, pady=2)
        
        ttk.Label(self.manual_frame, text="Mật khẩu:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = ttk.Entry(self.manual_frame, width=30, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=2)
        
        ttk.Label(self.manual_frame, text="Họ tên:").grid(row=2, column=0, sticky='w', pady=2)
        self.fullname_entry = ttk.Entry(self.manual_frame, width=30)
        self.fullname_entry.grid(row=2, column=1, padx=10, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x', pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="🚀 Bắt đầu Demo", 
                                   command=self.start_demo)
        self.start_btn.pack(side='left', padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ Dừng", 
                                  command=self.stop_demo, state='disabled')
        self.stop_btn.pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="🗑️ Xóa kết quả", 
                  command=self.clear_results).pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="📤 Xuất file", 
                  command=self.export_results).pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(main_frame, text="Kết quả đăng ký Demo", padding=10)
        results_frame.pack(fill='x', pady=5)
        
        # Results table
        columns = ('STT', 'Username', 'Password', 'Họ tên', 'Ngân hàng', 'Trạng thái')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == 'STT':
                self.results_tree.column(col, width=50)
            elif col == 'Password':
                self.results_tree.column(col, width=100)
            else:
                self.results_tree.column(col, width=120)
        
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_tree.pack(side='left', fill='both', expand=True)
        results_scrollbar.pack(side='right', fill='y')
        
        # Results info
        self.results_info_label = ttk.Label(results_frame, text="Chưa có kết quả")
        self.results_info_label.pack(pady=5)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Log Demo", padding=10)
        log_frame.pack(fill='both', expand=True, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10)
        self.log_text.pack(fill='both', expand=True)
        
        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')
        
        self.status_label = ttk.Label(self.status_frame, text="Demo Mode - Sẵn sàng")
        self.status_label.pack(side='left', padx=10)
        
        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.progress.pack(side='right', padx=10, pady=2)

    def on_mode_change(self):
        """Xử lý khi thay đổi chế độ"""
        if self.mode_var.get() == "manual":
            self.manual_frame.pack(fill='x', pady=5)
            self.count_var.set("1")
        else:
            self.manual_frame.pack_forget()

    def log_message(self, message, level="INFO"):
        """Ghi log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Màu sắc
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")

    def start_demo(self):
        """Bắt đầu demo"""
        if self.is_running:
            return
        
        # Validation
        try:
            count = int(self.count_var.get())
            success_rate = int(self.success_rate_var.get())
            if count <= 0 or success_rate < 0 or success_rate > 100:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ!")
            return
        
        # Kiểm tra thông tin thủ công
        if self.mode_var.get() == "manual":
            if not all([self.username_entry.get(), self.password_entry.get(), self.fullname_entry.get()]):
                messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ thông tin!")
                return
        
        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        self.status_label.config(text="Đang chạy Demo...")
        
        # Chạy trong thread riêng
        thread = threading.Thread(target=self.run_demo_process, daemon=True)
        thread.start()

    def stop_demo(self):
        """Dừng demo"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Demo Mode - Đã dừng")
        self.log_message("Đã dừng demo bởi người dùng", "WARNING")

    def run_demo_process(self):
        """Chạy quá trình demo"""
        try:
            count = int(self.count_var.get())
            success_rate = int(self.success_rate_var.get())
            mode = self.mode_var.get()
            
            self.log_message("🚀 Bắt đầu Demo Mode - Tương thích Chrome 137", "SUCCESS")
            self.log_message(f"📋 Chế độ: {mode}")
            self.log_message(f"🔢 Số lượng: {count}")
            self.log_message(f"📊 Tỷ lệ thành công: {success_rate}%")
            
            # Chuẩn bị thông tin thủ công
            manual_info = None
            if mode == "manual":
                manual_info = {
                    'username': self.username_entry.get(),
                    'password': self.password_entry.get(),
                    'fullname': self.fullname_entry.get()
                }
                self.log_message(f"👤 Thông tin thủ công: {manual_info['username']} - {manual_info['fullname']}")
            
            successful = 0
            failed = 0
            
            # Danh sách ngân hàng Việt Nam
            banks = ["Vietcombank", "Techcombank", "BIDV", "VietinBank", "ACB", "Sacombank", "MBBank", "VPBank", "TPBank", "HDBank"]
            
            for i in range(count):
                if not self.is_running:
                    break
                
                self.log_message(f"🔄 Đang xử lý tài khoản #{i+1}/{count}")
                
                # Simulate processing time
                for j in range(random.randint(3, 8)):
                    if not self.is_running:
                        break
                    time.sleep(0.5)
                
                if self.is_running:
                    # Generate account data
                    if mode == "manual" and manual_info:
                        username = manual_info['username']
                        password = manual_info['password']
                        fullname = manual_info['fullname']
                    else:
                        username = f"user{random.randint(10000, 99999)}"
                        password = f"pass{random.randint(1000, 9999)}"
                        
                        # Vietnamese names
                        first_names = ["Nguyễn", "Trần", "Lê", "Phạm", "Hoàng", "Huỳnh", "Phan", "Vũ", "Võ", "Đặng"]
                        middle_names = ["Văn", "Thị", "Minh", "Hoàng", "Thanh", "Quang", "Hữu", "Đức", "Anh", "Thành"]
                        last_names = ["An", "Bình", "Cường", "Dũng", "Hùng", "Khang", "Long", "Nam", "Phong", "Quân"]
                        
                        fullname = f"{random.choice(first_names)} {random.choice(middle_names)} {random.choice(last_names)}"
                    
                    bank_name = random.choice(banks)
                    
                    # Determine success/failure based on rate
                    is_success = random.randint(1, 100) <= success_rate
                    
                    if is_success:
                        successful += 1
                        status = "Thành công (Demo)"
                        self.log_message(f"✅ Tài khoản #{i+1}: {username} - THÀNH CÔNG", "SUCCESS")
                    else:
                        failed += 1
                        status = "Thất bại (Demo)"
                        self.log_message(f"❌ Tài khoản #{i+1}: {username} - THẤT BẠI", "ERROR")
                    
                    # Add to results table
                    self.add_result_to_table(i+1, username, password, fullname, bank_name, status)
                
                # Delay between accounts
                if i < count - 1 and self.is_running:
                    time.sleep(1)
            
            # Summary
            if self.is_running:
                total = successful + failed
                rate = (successful / total * 100) if total > 0 else 0
                self.log_message(f"🎉 Demo hoàn thành! Tổng: {total}, Thành công: {successful}, Thất bại: {failed}, Tỷ lệ: {rate:.1f}%", "SUCCESS")
                self.log_message("💡 Đây là Demo Mode - UI hiển thị đầy đủ như tool thật!", "INFO")
            
        except Exception as e:
            self.log_message(f"❌ Lỗi demo: {str(e)}", "ERROR")
        finally:
            self.is_running = False
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled'),
                self.progress.stop(),
                self.status_label.config(text="Demo Mode - Hoàn thành")
            ])

    def add_result_to_table(self, index, username, password, fullname, bank_name, status):
        """Thêm kết quả vào bảng"""
        try:
            self.results_tree.insert('', 'end', values=(
                index, username, password, fullname, bank_name, status
            ))
            
            # Scroll to bottom
            children = self.results_tree.get_children()
            if children:
                self.results_tree.see(children[-1])
            
            # Update info
            self.update_results_info()
                
        except Exception as e:
            self.log_message(f"Lỗi thêm kết quả: {e}", "ERROR")

    def update_results_info(self):
        """Cập nhật thông tin kết quả"""
        try:
            children = self.results_tree.get_children()
            total = len(children)
            
            if total == 0:
                self.results_info_label.config(text="Chưa có kết quả")
                return
            
            success_count = 0
            for child in children:
                values = self.results_tree.item(child)['values']
                if len(values) > 5 and "Thành công" in str(values[5]):
                    success_count += 1
            
            failed_count = total - success_count
            rate = (success_count / total * 100) if total > 0 else 0
            
            info_text = f"Tổng: {total} | Thành công: {success_count} | Thất bại: {failed_count} | Tỷ lệ: {rate:.1f}%"
            self.results_info_label.config(text=info_text)
            
        except Exception as e:
            self.log_message(f"Lỗi cập nhật thông tin: {e}", "ERROR")

    def clear_results(self):
        """Xóa kết quả"""
        try:
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            self.update_results_info()
            self.log_message("🗑️ Đã xóa kết quả", "INFO")
        except Exception as e:
            self.log_message(f"Lỗi xóa kết quả: {e}", "ERROR")

    def export_results(self):
        """Xuất kết quả ra file"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Lưu kết quả Demo",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Chrome Auto Registration Tool - Demo Mode Results\n")
                    f.write("=" * 60 + "\n")
                    f.write(f"Exported: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("Compatible with Chrome 137\n\n")
                    
                    if filename.endswith('.csv'):
                        f.write("STT,Username,Password,Họ tên,Ngân hàng,Trạng thái\n")
                        separator = ","
                    else:
                        f.write("STT\tUsername\tPassword\tHọ tên\tNgân hàng\tTrạng thái\n")
                        separator = "\t"
                    
                    for child in self.results_tree.get_children():
                        values = self.results_tree.item(child)['values']
                        f.write(separator.join(str(v) for v in values) + '\n')
                
                self.log_message(f"📤 Đã xuất kết quả ra: {filename}", "SUCCESS")
                
        except Exception as e:
            self.log_message(f"Lỗi xuất file: {e}", "ERROR")

    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DemoModeUI()
    app.run()
