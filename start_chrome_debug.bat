@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title Khởi động Chrome Debug Mode

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  KHỞI ĐỘNG CHROME DEBUG MODE                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Đang khởi động Chrome với debug mode...
echo Port: 9222
echo.

REM Tìm Chrome executable
set CHROME_PATH=
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe"
    echo ✅ Tìm thấy Chrome: C:\Program Files\Google\Chrome\Application\chrome.exe
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    echo ✅ Tìm thấy Chrome: C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
) else (
    echo ❌ Không tìm thấy Chrome! <PERSON><PERSON> thử tìm trong PATH...
    where chrome.exe >nul 2>&1
    if !errorlevel! equ 0 (
        set "CHROME_PATH=chrome.exe"
        echo ✅ Tìm thấy Chrome trong PATH
    ) else (
        echo ❌ Không tìm thấy Chrome! Vui lòng:
        echo    1. Cài đặt Google Chrome
        echo    2. Hoặc thêm Chrome vào PATH
        echo    3. Hoặc chạy tool với chế độ tạo browser mới
        pause
        exit /b 1
    )
)

echo.

REM Đóng Chrome hiện có trước (nếu có)
echo 🔄 Đóng Chrome hiện có...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

REM Khởi động Chrome với debug mode
echo 🚀 Đang khởi động Chrome với debug mode...
start "" "%CHROME_PATH%" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug" --disable-web-security --disable-features=VizDisplayCompositor --no-first-run --no-default-browser-check

REM Chờ Chrome khởi động
echo ⏳ Chờ Chrome khởi động...
timeout /t 3 >nul

REM Kiểm tra Chrome đã khởi động chưa
netstat -an | find "9222" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Chrome debug mode đã khởi động thành công!
) else (
    echo ⚠️  Chrome có thể chưa sẵn sàng, vui lòng chờ thêm vài giây
)

echo.
echo ✅ Chrome đã khởi động với debug mode!
echo.
echo 📋 Thông tin:
echo    - Debug Port: 9222
echo    - User Data: %TEMP%\chrome_debug
echo    - Bạn có thể sử dụng Chrome này để duyệt web bình thường
echo    - Tool sẽ kết nối với Chrome này để auto điền form
echo.
echo ⚠️  LƯU Ý:
echo    - Không đóng cửa sổ Chrome này khi đang chạy tool
echo    - Có thể mở nhiều tab trong Chrome này
echo    - Tool sẽ tự động tạo tab mới để đăng ký
echo.
echo 🎯 Bước tiếp theo:
echo    1. Mở website 13win16.com trong Chrome này (tùy chọn)
echo    2. Chạy tool: python main.py
echo    3. Chọn "Sử dụng trình duyệt hiện có: y"
echo.

pause
