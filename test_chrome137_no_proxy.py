"""
Test Chrome137Bot No Proxy - Test Chrome137Bot không dùng proxy
"""

import sys
import os
from pathlib import Path

# Thêm src vào path
sys.path.append(str(Path(__file__).parent / "src"))

def test_chrome137_no_proxy():
    """Test Chrome137Bot không dùng proxy"""
    print("🧪 Test Chrome137Bot No Proxy")
    print("=" * 60)
    
    try:
        from chrome137_registration_bot import Chrome137RegistrationBot
        print("✅ Import Chrome137RegistrationBot thành công")
        
        # Callback để hiển thị log
        def log_callback(message, level):
            print(f"[{level}] {message}")
        
        # Tạo bot KHÔNG DÙNG PROXY
        print("\n🤖 Tạo Chrome137RegistrationBot (No Proxy)...")
        bot = Chrome137RegistrationBot(proxy=None, ui_callback=log_callback)
        print("✅ Tạo bot thành công (No Proxy)")
        
        # Test setup Chrome driver
        print("\n🌐 Test setup Chrome 137 driver...")
        if bot.setup_chrome_driver_137():
            print("✅ Setup Chrome 137 driver THÀNH CÔNG!")
            
            # Test navigate đến 13win16.com trực tiếp
            print("\n📱 Test navigate to 13win16.com (No Proxy)...")
            try:
                if bot.navigate_to_register_page():
                    print("✅ Navigate 13win16.com THÀNH CÔNG!")
                    print("✅ Có thể truy cập 13win16.com trực tiếp")
                    
                    # Test tạo account và đăng ký
                    print("\n🎲 Test đăng ký tài khoản thật...")
                    success, account, message = bot.register_account()
                    
                    print(f"\n📊 KẾT QUẢ ĐĂNG KÝ:")
                    print(f"Thành công: {success}")
                    print(f"Username: {account.get('username', 'N/A')}")
                    print(f"Password: {account.get('password', 'N/A')}")
                    print(f"Full Name: {account.get('full_name', 'N/A')}")
                    print(f"Email: {account.get('email', 'N/A')}")
                    print(f"Thông báo: {message}")
                    
                    if success:
                        print("\n🎉 ĐĂNG KÝ THẬT THÀNH CÔNG!")
                        print("✅ Chrome137Bot hoạt động hoàn hảo")
                        print("✅ Không cần proxy")
                        print("✅ Có thể đăng ký thật trên 13win16.com")
                    else:
                        print("\n⚠️ Đăng ký thất bại nhưng Chrome137Bot hoạt động")
                        print("💡 Có thể do website thay đổi hoặc captcha")
                    
                    return True
                else:
                    print("❌ Navigate 13win16.com thất bại")
                    print("💡 Có thể do DNS hoặc website down")
                    return False
                    
            except Exception as e:
                print(f"❌ Lỗi navigate/đăng ký: {e}")
                return False
            finally:
                # Đóng driver
                if bot.driver:
                    bot.driver.quit()
        else:
            print("❌ Setup Chrome 137 driver thất bại")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test Chrome137Bot: {e}")
        return False

def test_simple_bot_no_proxy():
    """Test SimpleBot không dùng proxy"""
    print("\n🧪 Test SimpleBot No Proxy")
    print("=" * 60)
    
    try:
        from simple_registration_bot import SimpleRegistrationBot
        print("✅ Import SimpleRegistrationBot thành công")
        
        # Callback để hiển thị log
        def log_callback(message, level):
            print(f"[{level}] {message}")
        
        # Tạo bot KHÔNG DÙNG PROXY
        print("\n🤖 Tạo SimpleRegistrationBot (No Proxy)...")
        bot = SimpleRegistrationBot(proxy=None, ui_callback=log_callback)
        print("✅ Tạo bot thành công (No Proxy)")
        
        # Test đăng ký
        print("\n🎲 Test đăng ký mô phỏng...")
        success, account, message = bot.register_account()
        
        print(f"\n📊 KẾT QUẢ MÔ PHỎNG:")
        print(f"Thành công: {success}")
        print(f"Username: {account.get('username', 'N/A')}")
        print(f"Password: {account.get('password', 'N/A')}")
        print(f"Full Name: {account.get('full_name', 'N/A')}")
        print(f"Email: {account.get('email', 'N/A')}")
        print(f"Thông báo: {message}")
        
        if success:
            print("\n✅ SIMPLEBOT HOẠT ĐỘNG HOÀN HẢO!")
            print("✅ Mô phỏng đăng ký thành công")
            print("✅ Hiển thị username, password thật")
        
        return success
        
    except Exception as e:
        print(f"❌ Lỗi test SimpleBot: {e}")
        return False

def main():
    """Hàm main"""
    print("🚀 Test No Proxy Mode")
    print("Kiểm tra Chrome137Bot và SimpleBot không dùng proxy")
    print()
    
    # Test Chrome137Bot
    success1 = test_chrome137_no_proxy()
    
    # Test SimpleBot
    success2 = test_simple_bot_no_proxy()
    
    # Kết quả tổng
    print("\n" + "=" * 60)
    print("📊 KẾT QUẢ TỔNG:")
    print(f"Chrome137Bot (No Proxy): {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"SimpleBot (No Proxy): {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1:
        print("\n🎉 CHROME137BOT HOẠT ĐỘNG HOÀN HẢO!")
        print("✅ Có thể đăng ký thật trên 13win16.com")
        print("✅ Không cần proxy")
        print("✅ ChromeDriver local hoạt động")
        
        print("\n💡 Bây giờ có thể chạy:")
        print("   start_chrome137_no_proxy.bat")
        print("   set NO_PROXY=1 && python ui/simple_ui.py")
        
    elif success2:
        print("\n⚠️ Chrome137Bot lỗi nhưng SimpleBot OK")
        print("✅ Vẫn có thể dùng mô phỏng")
        print("✅ Hiển thị username, password thật")
        
        print("\n💡 Bây giờ có thể chạy:")
        print("   demo_mode.bat")
        print("   Hoặc tick 'Chế độ nhanh' trong UI")
        
    else:
        print("\n❌ CẢ HAI ĐỀU LỖI!")
        print("💡 Kiểm tra:")
        print("   - Internet connection")
        print("   - Chrome installation")
        print("   - Python dependencies")
    
    print("=" * 60)
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
