"""
Simple Registration Bot - Version đơn gi<PERSON>n không cần ChromeDriver
"""

import time
import random
import logging
from pathlib import Path
from datetime import datetime

from account_generator import AccountGenerator

class SimpleRegistrationBot:
    def __init__(self, proxy=None, ui_callback=None):
        self.proxy = proxy
        self.ui_callback = ui_callback
        self.account_generator = AccountGenerator()
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Thiết lập logging"""
        try:
            log_file = Path(__file__).parent.parent / "data" / "logs" / "registration.log"
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_file, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )
            self.logger = logging.getLogger(__name__)
        except Exception as e:
            print(f"Lỗi setup logging: {e}")
            self.logger = logging.getLogger(__name__)

    def log(self, message, level="INFO"):
        """Ghi log và gửi về UI"""
        try:
            if level == "INFO":
                self.logger.info(message)
            elif level == "ERROR":
                self.logger.error(message)
            elif level == "WARNING":
                self.logger.warning(message)
            elif level == "SUCCESS":
                self.logger.info(f"SUCCESS: {message}")
            
            if self.ui_callback:
                self.ui_callback(message, level)
        except Exception as e:
            print(f"Lỗi log: {e}")

    def simulate_registration_process(self, account_data):
        """Mô phỏng quá trình đăng ký"""
        try:
            self.log(f"🔄 Bắt đầu đăng ký: {account_data['username']}")
            
            # Mô phỏng truy cập trang
            self.log("📱 Đang truy cập 13win16.com...")
            time.sleep(random.uniform(2, 4))
            
            # Mô phỏng điền form
            self.log("📝 Đang điền form đăng ký...")
            self.log(f"   ✓ Username: {account_data['username']}")
            time.sleep(random.uniform(1, 2))
            
            self.log("   ✓ Password: ********")
            time.sleep(random.uniform(1, 2))
            
            self.log(f"   ✓ Họ tên: {account_data['full_name']}")
            time.sleep(random.uniform(1, 2))
            
            self.log(f"   ✓ Email: {account_data['email']}")
            time.sleep(random.uniform(1, 2))
            
            # Mô phỏng submit
            self.log("🚀 Đang submit form...")
            time.sleep(random.uniform(2, 3))
            
            # Mô phỏng kết quả (85% thành công)
            success_rate = 85
            is_success = random.randint(1, 100) <= success_rate
            
            if is_success:
                self.log("✅ Đăng ký thành công!", "SUCCESS")
                return True, "Đăng ký thành công (Simulated)"
            else:
                error_messages = [
                    "Username đã tồn tại",
                    "Email đã được sử dụng", 
                    "Lỗi kết nối",
                    "Captcha không đúng",
                    "Thông tin không hợp lệ"
                ]
                error_msg = random.choice(error_messages)
                self.log(f"❌ Đăng ký thất bại: {error_msg}", "ERROR")
                return False, f"Đăng ký thất bại: {error_msg}"
                
        except Exception as e:
            self.log(f"❌ Lỗi trong quá trình đăng ký: {e}", "ERROR")
            return False, f"Lỗi: {str(e)}"

    def register_account(self, manual_info=None):
        """Đăng ký một tài khoản"""
        try:
            # Tạo thông tin tài khoản
            if manual_info:
                account_data = manual_info
                self.log(f"📋 Sử dụng thông tin thủ công: {account_data.get('username', 'N/A')}")
            else:
                self.log("🎲 Tạo thông tin tài khoản ngẫu nhiên...")
                account_data = self.account_generator.generate_account()
                self.log(f"✓ Đã tạo tài khoản: {account_data['username']}")
            
            # Thêm thông tin proxy nếu có
            if self.proxy:
                self.log(f"🌐 Sử dụng proxy: {self.proxy.get('ip', 'N/A')}:{self.proxy.get('port', 'N/A')}")
            
            # Mô phỏng quá trình đăng ký
            success, message = self.simulate_registration_process(account_data)
            
            if success:
                self.log(f"🎉 Hoàn thành đăng ký: {account_data['username']}", "SUCCESS")
                return True, account_data, message
            else:
                self.log(f"💥 Thất bại: {account_data['username']} - {message}", "ERROR")
                return False, account_data, message
                
        except Exception as e:
            self.log(f"❌ Lỗi trong register_account: {e}", "ERROR")
            return False, account_data if 'account_data' in locals() else {}, str(e)

    def save_result(self, success, account_data, message):
        """Lưu kết quả đăng ký"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Tạo thư mục data
            data_dir = Path(__file__).parent.parent / "data" / "accounts"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            if success:
                success_file = data_dir / "successful_accounts.txt"
                with open(success_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {account_data.get('password', 'N/A')} | {account_data.get('full_name', 'N/A')} | 13win16.com | {message}\n")
                self.log(f"💾 Đã lưu tài khoản thành công: {success_file}")
            else:
                failed_file = data_dir / "failed_accounts.txt"
                with open(failed_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {message}\n")
                self.log(f"💾 Đã lưu tài khoản thất bại: {failed_file}")
                    
        except Exception as e:
            self.log(f"❌ Lỗi lưu kết quả: {e}", "ERROR")

    def get_registration_stats(self):
        """Lấy thống kê đăng ký"""
        try:
            data_dir = Path(__file__).parent.parent / "data" / "accounts"
            success_file = data_dir / "successful_accounts.txt"
            failed_file = data_dir / "failed_accounts.txt"
            
            success_count = 0
            failed_count = 0
            
            if success_file.exists():
                with open(success_file, 'r', encoding='utf-8') as f:
                    success_count = len(f.readlines())
            
            if failed_file.exists():
                with open(failed_file, 'r', encoding='utf-8') as f:
                    failed_count = len(f.readlines())
            
            total = success_count + failed_count
            success_rate = (success_count / total * 100) if total > 0 else 0
            
            return {
                'total': total,
                'success': success_count,
                'failed': failed_count,
                'success_rate': success_rate
            }
            
        except Exception as e:
            self.log(f"❌ Lỗi lấy thống kê: {e}", "ERROR")
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0}

# Test function
if __name__ == "__main__":
    def test_callback(message, level):
        print(f"[{level}] {message}")
    
    print("🧪 Test SimpleRegistrationBot")
    print("=" * 50)
    
    # Test 1: Tạo bot
    bot = SimpleRegistrationBot(ui_callback=test_callback)
    print("✅ Tạo bot thành công")
    
    # Test 2: Đăng ký tự động
    print("\n🔄 Test đăng ký tự động...")
    success, account, message = bot.register_account()
    print(f"Kết quả: {success}")
    print(f"Tài khoản: {account.get('username', 'N/A')}")
    print(f"Thông báo: {message}")
    
    # Test 3: Lưu kết quả
    bot.save_result(success, account, message)
    
    # Test 4: Thống kê
    stats = bot.get_registration_stats()
    print(f"\n📊 Thống kê: {stats}")
    
    print("\n🎉 Test hoàn thành!")
    input("Nhấn Enter để thoát...")
