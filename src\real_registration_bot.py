"""
Real Registration Bot - Thực sự đăng ký tài khoản trên 13win16.com
"""

import time
import random
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from pathlib import Path
import requests
from datetime import datetime

from config import (
    REGISTER_URL, BROWSER_CONFIG, FORM_SELECTORS, 
    DELAY_CONFIG, OUTPUT_CONFIG
)
from account_generator import AccountGenerator

class RealRegistrationBot:
    def __init__(self, proxy=None, ui_callback=None):
        self.proxy = proxy
        self.ui_callback = ui_callback
        self.driver = None
        self.wait = None
        self.account_generator = AccountGenerator()
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Thiết lập logging"""
        log_file = Path(__file__).parent.parent / OUTPUT_CONFIG["log_file"]
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def log(self, message, level="INFO"):
        """Ghi log và gửi về UI"""
        if level == "INFO":
            self.logger.info(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "WARNING":
            self.logger.warning(message)
        
        if self.ui_callback:
            self.ui_callback(message, level)

    def setup_chrome_driver(self):
        """Thiết lập Chrome driver"""
        try:
            chrome_options = Options()
            
            # Cấu hình Chrome
            if BROWSER_CONFIG.get("headless", False):
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument(f"--window-size={BROWSER_CONFIG['window_size'][0]},{BROWSER_CONFIG['window_size'][1]}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # User agent
            if BROWSER_CONFIG.get("user_agent_rotation", True):
                user_agents = [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                ]
                chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
            
            # Proxy
            if self.proxy:
                proxy_string = f"{self.proxy['ip']}:{self.proxy['port']}"
                chrome_options.add_argument(f"--proxy-server=http://{proxy_string}")
                self.log(f"Sử dụng proxy: {proxy_string}")
            
            # Thử kết nối với Chrome hiện có trước
            if BROWSER_CONFIG.get("use_existing_browser", True):
                try:
                    chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{BROWSER_CONFIG['debug_port']}")
                    self.driver = webdriver.Chrome(options=chrome_options)
                    self.log("Đã kết nối với Chrome hiện có")
                except Exception as e:
                    self.log(f"Không thể kết nối với Chrome hiện có: {e}", "WARNING")
                    raise e
            
            # Nếu không kết nối được, tạo Chrome mới
            if not self.driver:
                self.log("Tạo Chrome mới...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Cấu hình driver
            self.driver.set_page_load_timeout(BROWSER_CONFIG["page_load_timeout"])
            self.driver.implicitly_wait(BROWSER_CONFIG["implicit_wait"])
            
            # Ẩn automation
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, DELAY_CONFIG["element_wait"])
            
            self.log("Chrome driver đã sẵn sàng")
            return True
            
        except Exception as e:
            self.log(f"Lỗi khi thiết lập Chrome driver: {e}", "ERROR")
            return False

    def navigate_to_register_page(self):
        """Điều hướng đến trang đăng ký"""
        try:
            self.log(f"Đang truy cập: {REGISTER_URL}")
            self.driver.get(REGISTER_URL)
            
            # Chờ trang load
            time.sleep(DELAY_CONFIG["page_load_wait"])
            
            # Kiểm tra trang đã load
            if "13win" in self.driver.title.lower() or "register" in self.driver.current_url.lower():
                self.log("Đã truy cập trang đăng ký thành công")
                return True
            else:
                self.log(f"Trang không đúng. Title: {self.driver.title}", "WARNING")
                return True  # Vẫn tiếp tục thử
                
        except Exception as e:
            self.log(f"Lỗi khi truy cập trang đăng ký: {e}", "ERROR")
            return False

    def find_element_by_selectors(self, selectors, timeout=10):
        """Tìm element bằng nhiều selector"""
        if isinstance(selectors, str):
            selectors = [selectors]
        
        for selector in selectors:
            try:
                # Thử CSS selector
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                return element
            except:
                try:
                    # Thử XPath
                    element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located((By.XPATH, f"//*[contains(@placeholder, '{selector}') or contains(@name, '{selector}')]"))
                    )
                    return element
                except:
                    continue
        
        return None

    def fill_registration_form(self, account_data):
        """Điền form đăng ký"""
        try:
            self.log("Bắt đầu điền form đăng ký...")
            
            # Chờ form xuất hiện
            time.sleep(2)
            
            # 1. Điền username/phone
            username_field = self.find_element_by_selectors([
                'input[placeholder*="Số điện thoại"]',
                'input[placeholder*="Tên Đăng Nhập"]',
                'input[name="username"]',
                'input[name="phone"]',
                'input[type="tel"]'
            ])
            
            if username_field:
                username_field.clear()
                username_field.send_keys(account_data['username'])
                self.log(f"Đã điền username: {account_data['username']}")
                time.sleep(random.uniform(*DELAY_CONFIG["after_input"]))
            else:
                self.log("Không tìm thấy trường username/phone", "ERROR")
                return False
            
            # 2. Điền password
            password_field = self.find_element_by_selectors([
                'input[placeholder*="Mật khẩu"]',
                'input[name="password"]',
                'input[type="password"]'
            ])
            
            if password_field:
                password_field.clear()
                password_field.send_keys(account_data['password'])
                self.log("Đã điền password")
                time.sleep(random.uniform(*DELAY_CONFIG["after_input"]))
            else:
                self.log("Không tìm thấy trường password", "ERROR")
                return False
            
            # 3. Điền confirm password
            confirm_password_field = self.find_element_by_selectors([
                'input[placeholder*="xác nhận"]',
                'input[placeholder*="Xác nhận"]',
                'input[name="confirmPassword"]',
                'input[name="confirm_password"]'
            ])
            
            if confirm_password_field:
                confirm_password_field.clear()
                confirm_password_field.send_keys(account_data['password'])
                self.log("Đã điền confirm password")
                time.sleep(random.uniform(*DELAY_CONFIG["after_input"]))
            
            # 4. Điền họ tên thật
            fullname_field = self.find_element_by_selectors([
                'input[placeholder*="Họ Tên"]',
                'input[placeholder*="Tên Thật"]',
                'input[name="realName"]',
                'input[name="fullName"]'
            ])
            
            if fullname_field:
                fullname_field.clear()
                fullname_field.send_keys(account_data['full_name'])
                self.log(f"Đã điền họ tên: {account_data['full_name']}")
                time.sleep(random.uniform(*DELAY_CONFIG["after_input"]))
            
            # 5. Tick checkbox điều khoản (nếu có)
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    self.log("Đã tick checkbox điều khoản")
                    time.sleep(random.uniform(*DELAY_CONFIG["after_click"]))
            except:
                self.log("Không tìm thấy checkbox điều khoản", "WARNING")
            
            self.log("Đã điền xong form đăng ký")
            return True
            
        except Exception as e:
            self.log(f"Lỗi khi điền form: {e}", "ERROR")
            return False

    def submit_registration(self):
        """Submit form đăng ký"""
        try:
            self.log("Đang submit form đăng ký...")
            
            # Tìm nút submit
            submit_button = self.find_element_by_selectors([
                'button:contains("ĐĂNG KÝ")',
                'button[type="submit"]',
                'input[type="submit"]',
                '.submit-btn',
                'button.btn-primary',
                'button.register-btn'
            ])
            
            if not submit_button:
                # Thử tìm bằng text
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "đăng ký" in button.text.lower() or "register" in button.text.lower():
                        submit_button = button
                        break
            
            if submit_button:
                # Scroll đến button
                self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                time.sleep(1)
                
                # Click submit
                submit_button.click()
                self.log("Đã click nút đăng ký")
                
                # Chờ xử lý
                time.sleep(DELAY_CONFIG["after_submit"])
                
                return True
            else:
                self.log("Không tìm thấy nút submit", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Lỗi khi submit form: {e}", "ERROR")
            return False

    def check_registration_result(self):
        """Kiểm tra kết quả đăng ký"""
        try:
            self.log("Đang kiểm tra kết quả đăng ký...")
            
            # Chờ response
            time.sleep(3)
            
            current_url = self.driver.current_url
            page_source = self.driver.page_source.lower()
            
            # Kiểm tra thành công
            success_indicators = [
                "thành công", "success", "welcome", "chào mừng",
                "đăng ký thành công", "registration successful",
                "dashboard", "profile", "account"
            ]
            
            # Kiểm tra thất bại
            error_indicators = [
                "lỗi", "error", "failed", "thất bại",
                "đã tồn tại", "already exists", "invalid",
                "không hợp lệ", "captcha", "verification"
            ]
            
            # Kiểm tra URL thay đổi (thường là thành công)
            if current_url != REGISTER_URL and "register" not in current_url:
                self.log("URL đã thay đổi - có thể đăng ký thành công")
                return True, "Đăng ký thành công (URL changed)"
            
            # Kiểm tra nội dung trang
            for indicator in success_indicators:
                if indicator in page_source:
                    self.log(f"Tìm thấy indicator thành công: {indicator}")
                    return True, f"Đăng ký thành công ({indicator})"
            
            for indicator in error_indicators:
                if indicator in page_source:
                    self.log(f"Tìm thấy indicator lỗi: {indicator}")
                    return False, f"Đăng ký thất bại ({indicator})"
            
            # Không xác định được
            self.log("Không xác định được kết quả đăng ký", "WARNING")
            return False, "Không xác định được kết quả"
            
        except Exception as e:
            self.log(f"Lỗi khi kiểm tra kết quả: {e}", "ERROR")
            return False, f"Lỗi kiểm tra: {str(e)}"

    def take_screenshot(self, filename_suffix=""):
        """Chụp screenshot"""
        try:
            screenshots_dir = Path(__file__).parent.parent / OUTPUT_CONFIG["screenshots_dir"]
            screenshots_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"registration_{timestamp}_{filename_suffix}.png"
            filepath = screenshots_dir / filename
            
            self.driver.save_screenshot(str(filepath))
            self.log(f"Đã chụp screenshot: {filename}")
            return str(filepath)
            
        except Exception as e:
            self.log(f"Lỗi chụp screenshot: {e}", "ERROR")
            return None

    def register_account(self, manual_info=None):
        """Đăng ký một tài khoản"""
        try:
            # Tạo thông tin tài khoản
            if manual_info:
                account_data = manual_info
            else:
                account_data = self.account_generator.generate_account()
            
            self.log(f"Bắt đầu đăng ký tài khoản: {account_data['username']}")
            
            # Thiết lập driver
            if not self.setup_chrome_driver():
                return False, account_data, "Không thể thiết lập Chrome driver"
            
            # Truy cập trang đăng ký
            if not self.navigate_to_register_page():
                return False, account_data, "Không thể truy cập trang đăng ký"
            
            # Chụp screenshot trước khi điền
            self.take_screenshot("before_fill")
            
            # Điền form
            if not self.fill_registration_form(account_data):
                self.take_screenshot("fill_error")
                return False, account_data, "Không thể điền form đăng ký"
            
            # Chụp screenshot sau khi điền
            self.take_screenshot("after_fill")
            
            # Submit form
            if not self.submit_registration():
                self.take_screenshot("submit_error")
                return False, account_data, "Không thể submit form"
            
            # Kiểm tra kết quả
            success, message = self.check_registration_result()
            
            # Chụp screenshot kết quả
            self.take_screenshot("result")
            
            if success:
                self.log(f"Đăng ký thành công: {account_data['username']}", "SUCCESS")
                return True, account_data, message
            else:
                self.log(f"Đăng ký thất bại: {message}", "ERROR")
                return False, account_data, message
                
        except Exception as e:
            self.log(f"Lỗi trong quá trình đăng ký: {e}", "ERROR")
            self.take_screenshot("exception")
            return False, account_data if 'account_data' in locals() else {}, str(e)
        
        finally:
            # Đóng driver nếu không sử dụng existing browser
            if self.driver and not BROWSER_CONFIG.get("use_existing_browser", True):
                try:
                    self.driver.quit()
                except:
                    pass

    def save_result(self, success, account_data, message):
        """Lưu kết quả đăng ký"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if success:
                success_file = Path(__file__).parent.parent / OUTPUT_CONFIG["success_file"]
                success_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(success_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {account_data.get('password', 'N/A')} | {account_data.get('full_name', 'N/A')} | {message}\n")
            else:
                failed_file = Path(__file__).parent.parent / OUTPUT_CONFIG["failed_file"]
                failed_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(failed_file, 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | {account_data.get('username', 'N/A')} | {message}\n")
                    
        except Exception as e:
            self.log(f"Lỗi lưu kết quả: {e}", "ERROR")

# Test function
if __name__ == "__main__":
    def test_callback(message, level):
        print(f"[{level}] {message}")
    
    bot = RealRegistrationBot(ui_callback=test_callback)
    success, account, message = bot.register_account()
    
    print(f"Kết quả: {success}")
    print(f"Tài khoản: {account}")
    print(f"Thông báo: {message}")
    
    bot.save_result(success, account, message)
