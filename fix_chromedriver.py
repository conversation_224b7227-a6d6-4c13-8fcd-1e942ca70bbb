"""
Fix ChromeDriver issues - Tự động tải và cài đặt ChromeDriver đúng
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path
import subprocess
import json

def get_chrome_version():
    """Lấy version của Chrome đã cài đặt"""
    try:
        # Windows
        if os.name == 'nt':
            import winreg
            
            # Thử các registry key khác nhau
            possible_keys = [
                r"SOFTWARE\Google\Chrome\BLBeacon",
                r"SOFTWARE\Wow6432Node\Google\Chrome\BLBeacon",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome"
            ]
            
            for key_path in possible_keys:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                        version, _ = winreg.QueryValueEx(key, "version")
                        return version
                except:
                    continue
            
            # Thử command line
            try:
                result = subprocess.run([
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe", "--version"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    return version
            except:
                pass
                
            # Thử path khác
            try:
                result = subprocess.run([
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "--version"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    return version
            except:
                pass
        
        print("❌ Không thể tìm thấy Chrome version")
        return None
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy Chrome version: {e}")
        return None

def get_chromedriver_download_url(chrome_version):
    """Lấy URL download ChromeDriver phù hợp"""
    try:
        # Lấy major version
        major_version = chrome_version.split('.')[0]
        
        # API mới của ChromeDriver
        api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
        
        print(f"🔍 Tìm ChromeDriver cho Chrome {chrome_version}...")
        
        response = requests.get(api_url, timeout=30)
        if response.status_code == 200:
            data = response.json()
            
            # Tìm version phù hợp
            for version_info in reversed(data['versions']):
                if version_info['version'].startswith(major_version + '.'):
                    downloads = version_info.get('downloads', {})
                    chromedriver_downloads = downloads.get('chromedriver', [])
                    
                    # Tìm Windows version
                    for download in chromedriver_downloads:
                        if download['platform'] == 'win64':
                            return download['url'], version_info['version']
        
        print(f"❌ Không tìm thấy ChromeDriver cho Chrome {chrome_version}")
        return None, None
        
    except Exception as e:
        print(f"❌ Lỗi khi tìm ChromeDriver: {e}")
        return None, None

def download_and_install_chromedriver(url, version):
    """Tải và cài đặt ChromeDriver"""
    try:
        print(f"📥 Đang tải ChromeDriver {version}...")
        
        # Tạo thư mục drivers
        drivers_dir = Path(__file__).parent / "drivers"
        drivers_dir.mkdir(exist_ok=True)
        
        # Tải file
        response = requests.get(url, timeout=60)
        if response.status_code == 200:
            zip_path = drivers_dir / "chromedriver.zip"
            
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            print("📦 Đang giải nén...")
            
            # Giải nén
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Tìm file chromedriver.exe
            chromedriver_exe = None
            for root, dirs, files in os.walk(drivers_dir):
                for file in files:
                    if file == "chromedriver.exe":
                        chromedriver_exe = Path(root) / file
                        break
                if chromedriver_exe:
                    break
            
            if chromedriver_exe:
                # Di chuyển về thư mục drivers chính
                final_path = drivers_dir / "chromedriver.exe"
                if final_path.exists():
                    final_path.unlink()
                
                shutil.move(str(chromedriver_exe), str(final_path))
                
                # Xóa file zip và thư mục tạm
                zip_path.unlink()
                for item in drivers_dir.iterdir():
                    if item.is_dir() and item.name.startswith("chromedriver"):
                        shutil.rmtree(item)
                
                print(f"✅ Đã cài đặt ChromeDriver {version} thành công!")
                print(f"📁 Đường dẫn: {final_path}")
                return True
            else:
                print("❌ Không tìm thấy chromedriver.exe trong file zip")
                return False
        else:
            print(f"❌ Lỗi tải file: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi tải ChromeDriver: {e}")
        return False

def clean_old_chromedriver():
    """Xóa ChromeDriver cũ"""
    try:
        drivers_dir = Path(__file__).parent / "drivers"
        if drivers_dir.exists():
            for item in drivers_dir.iterdir():
                if item.name.startswith("chromedriver") and item.is_file():
                    item.unlink()
                    print(f"🗑️ Đã xóa: {item}")
        
        # Xóa cache của webdriver-manager
        import tempfile
        wdm_cache = Path(tempfile.gettempdir()) / ".wdm"
        if wdm_cache.exists():
            shutil.rmtree(wdm_cache, ignore_errors=True)
            print("🗑️ Đã xóa cache webdriver-manager")
            
        # Xóa cache trong user folder
        user_wdm = Path.home() / ".wdm"
        if user_wdm.exists():
            shutil.rmtree(user_wdm, ignore_errors=True)
            print("🗑️ Đã xóa cache user webdriver-manager")
            
    except Exception as e:
        print(f"⚠️ Lỗi khi xóa file cũ: {e}")

def test_chromedriver():
    """Test ChromeDriver"""
    try:
        drivers_dir = Path(__file__).parent / "drivers"
        chromedriver_path = drivers_dir / "chromedriver.exe"
        
        if not chromedriver_path.exists():
            print("❌ Không tìm thấy chromedriver.exe")
            return False
        
        print("🧪 Đang test ChromeDriver...")
        
        result = subprocess.run([str(chromedriver_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ChromeDriver hoạt động: {version}")
            return True
        else:
            print(f"❌ ChromeDriver lỗi: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test ChromeDriver: {e}")
        return False

def main():
    """Hàm main"""
    print("🔧 Chrome Auto Registration Tool - ChromeDriver Fixer")
    print("=" * 60)
    
    # Lấy Chrome version
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("❌ Không thể tìm thấy Google Chrome!")
        print("Vui lòng cài đặt Google Chrome trước.")
        input("Nhấn Enter để thoát...")
        return
    
    print(f"✅ Tìm thấy Chrome version: {chrome_version}")
    
    # Xóa ChromeDriver cũ
    print("\n🗑️ Xóa ChromeDriver cũ...")
    clean_old_chromedriver()
    
    # Tìm ChromeDriver phù hợp
    print(f"\n🔍 Tìm ChromeDriver phù hợp...")
    download_url, driver_version = get_chromedriver_download_url(chrome_version)
    
    if not download_url:
        print("❌ Không tìm thấy ChromeDriver phù hợp!")
        input("Nhấn Enter để thoát...")
        return
    
    print(f"✅ Tìm thấy ChromeDriver {driver_version}")
    
    # Tải và cài đặt
    print(f"\n📥 Tải và cài đặt ChromeDriver...")
    if download_and_install_chromedriver(download_url, driver_version):
        print("\n🧪 Test ChromeDriver...")
        if test_chromedriver():
            print("\n🎉 Hoàn thành! ChromeDriver đã sẵn sàng.")
            print("\nBây giờ bạn có thể chạy tool:")
            print("- python simple_launcher.py")
            print("- start_simple.bat")
        else:
            print("\n❌ ChromeDriver không hoạt động đúng!")
    else:
        print("\n❌ Không thể cài đặt ChromeDriver!")
    
    input("\nNhấn Enter để thoát...")

if __name__ == "__main__":
    main()
