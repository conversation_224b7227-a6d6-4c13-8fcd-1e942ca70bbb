"""
Simple Chrome Fix - Version đơn giản và ổn định
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path
import subprocess

def print_step(message):
    """In thông báo với format đẹp"""
    print(f"🔧 {message}")

def get_chrome_version_simple():
    """Lấy Chrome version đơn giản"""
    try:
        print_step("Đang tìm Chrome version...")
        
        # Thử các đường dẫn Chrome phổ biến
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                try:
                    result = subprocess.run([chrome_path, "--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        print_step(f"Tìm thấy Chrome: {version}")
                        return version
                except:
                    continue
        
        print_step("❌ Không tìm thấy Chrome!")
        return None
        
    except Exception as e:
        print_step(f"❌ Lỗi: {e}")
        return None

def download_chromedriver_simple(chrome_version):
    """Tải ChromeDriver đơn giản"""
    try:
        print_step("Đang tải ChromeDriver...")
        
        # Lấy major version
        major_version = chrome_version.split('.')[0]
        
        # URL tải trực tiếp (stable versions)
        download_urls = [
            f"https://storage.googleapis.com/chrome-for-testing-public/{chrome_version}/win64/chromedriver-win64.zip",
            f"https://storage.googleapis.com/chrome-for-testing-public/{major_version}.0.0.0/win64/chromedriver-win64.zip"
        ]
        
        drivers_dir = Path(__file__).parent / "drivers"
        drivers_dir.mkdir(exist_ok=True)
        
        for url in download_urls:
            try:
                print_step(f"Thử tải từ: {url.split('/')[-3]}")
                
                response = requests.get(url, timeout=60)
                if response.status_code == 200:
                    zip_path = drivers_dir / "chromedriver.zip"
                    
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    print_step("Đang giải nén...")
                    
                    # Giải nén
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(drivers_dir)
                    
                    # Tìm và di chuyển chromedriver.exe
                    for root, dirs, files in os.walk(drivers_dir):
                        for file in files:
                            if file == "chromedriver.exe":
                                source = Path(root) / file
                                target = drivers_dir / "chromedriver.exe"
                                
                                if target.exists():
                                    target.unlink()
                                
                                shutil.move(str(source), str(target))
                                
                                # Dọn dẹp
                                zip_path.unlink()
                                for item in drivers_dir.iterdir():
                                    if item.is_dir() and "chromedriver" in item.name:
                                        shutil.rmtree(item)
                                
                                print_step(f"✅ Tải thành công: {target}")
                                return True
                
            except Exception as e:
                print_step(f"⚠️ Lỗi tải từ URL này: {str(e)[:50]}...")
                continue
        
        print_step("❌ Không thể tải ChromeDriver!")
        return False
        
    except Exception as e:
        print_step(f"❌ Lỗi tải ChromeDriver: {e}")
        return False

def clean_chrome_cache():
    """Xóa cache Chrome"""
    try:
        print_step("Đang xóa cache cũ...")
        
        # Xóa cache webdriver-manager
        cache_dirs = [
            Path.home() / ".wdm",
            Path(os.environ.get('TEMP', '')) / ".wdm",
            Path(__file__).parent / "drivers"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    if cache_dir.name == "drivers":
                        # Chỉ xóa chromedriver.exe trong drivers
                        for item in cache_dir.iterdir():
                            if "chromedriver" in item.name:
                                if item.is_file():
                                    item.unlink()
                                elif item.is_dir():
                                    shutil.rmtree(item)
                    else:
                        shutil.rmtree(cache_dir)
                    print_step(f"✅ Đã xóa: {cache_dir}")
                except:
                    pass
        
    except Exception as e:
        print_step(f"⚠️ Lỗi xóa cache: {e}")

def test_chromedriver():
    """Test ChromeDriver"""
    try:
        print_step("Đang test ChromeDriver...")
        
        chromedriver_path = Path(__file__).parent / "drivers" / "chromedriver.exe"
        
        if not chromedriver_path.exists():
            print_step("❌ Không tìm thấy chromedriver.exe")
            return False
        
        result = subprocess.run([str(chromedriver_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print_step(f"✅ ChromeDriver hoạt động: {version}")
            return True
        else:
            print_step(f"❌ ChromeDriver lỗi: {result.stderr}")
            return False
            
    except Exception as e:
        print_step(f"❌ Lỗi test: {e}")
        return False

def main():
    """Hàm main đơn giản"""
    print("=" * 60)
    print("🔧 Simple Chrome Fix - Sửa lỗi ChromeDriver")
    print("=" * 60)
    
    try:
        # Bước 1: Lấy Chrome version
        chrome_version = get_chrome_version_simple()
        if not chrome_version:
            print_step("❌ Không thể tiếp tục mà không có Chrome version")
            return False
        
        # Bước 2: Xóa cache cũ
        clean_chrome_cache()
        
        # Bước 3: Tải ChromeDriver
        if download_chromedriver_simple(chrome_version):
            # Bước 4: Test
            if test_chromedriver():
                print_step("🎉 Hoàn thành! ChromeDriver đã sẵn sàng")
                print_step("Bây giờ có thể chạy tool:")
                print_step("- start_simple.bat")
                print_step("- python simple_launcher.py")
                return True
            else:
                print_step("❌ ChromeDriver không hoạt động")
                return False
        else:
            print_step("❌ Không thể tải ChromeDriver")
            return False
            
    except Exception as e:
        print_step(f"❌ Lỗi chung: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n" + "=" * 60)
            print("💡 Hướng dẫn thủ công:")
            print("1. Mở Chrome → Help → About Google Chrome")
            print("2. Ghi nhớ version (VD: 131.0.6778.108)")
            print("3. Vào: https://googlechromelabs.github.io/chrome-for-testing/")
            print("4. Tải ChromeDriver cho version tương ứng")
            print("5. Giải nén và copy chromedriver.exe vào thư mục drivers/")
            print("=" * 60)
        
        input("\nNhấn Enter để thoát...")
        
    except KeyboardInterrupt:
        print("\n👋 Đã hủy bởi người dùng")
    except Exception as e:
        print(f"\n❌ Lỗi không mong đợi: {e}")
        input("Nhấn Enter để thoát...")
