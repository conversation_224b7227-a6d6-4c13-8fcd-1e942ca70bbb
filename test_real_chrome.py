"""
Test Real Chrome - Test RealRegistrationBot với Chrome thật
"""

import sys
from pathlib import Path

# Thêm src vào path
sys.path.append(str(Path(__file__).parent / "src"))

def test_real_chrome():
    """Test RealRegistrationBot với Chrome thật"""
    print("🌐 Test Real Chrome Registration")
    print("=" * 60)
    
    try:
        # Import
        from real_registration_bot import RealRegistrationBot
        print("✅ Import RealRegistrationBot thành công")
        
        # Callback để hiển thị log
        def log_callback(message, level):
            print(f"[{level}] {message}")
        
        # Tạo bot
        print("\n🤖 Tạo RealRegistrationBot...")
        bot = RealRegistrationBot(ui_callback=log_callback)
        print("✅ Tạo bot thành công")
        
        # Test tạo account
        print("\n🎲 Test tạo account...")
        account = bot.account_generator.generate_account()
        print(f"✅ Tạo account thành công: {account['username']}")
        print(f"   Username: {account['username']}")
        print(f"   Password: {account['password']}")
        print(f"   Full Name: {account['full_name']}")
        print(f"   Email: {account['email']}")
        
        # Hỏi user có muốn test thật không
        print("\n" + "=" * 60)
        print("🚨 CẢNH BÁO: Test tiếp theo sẽ mở Chrome thật!")
        print("✅ Sẽ truy cập 13win16.com và thử đăng ký")
        print("✅ Bạn có thể xem toàn bộ quá trình")
        print("⚠️ Cần ChromeDriver hoạt động")
        print("=" * 60)
        
        choice = input("Bạn có muốn test với Chrome thật? (y/n): ").strip().lower()
        
        if choice == 'y':
            print("\n🌐 Bắt đầu test với Chrome thật...")
            print("🔄 Đang khởi động Chrome...")
            
            # Test đăng ký thật
            success, account_result, message = bot.register_account()
            
            print("\n" + "=" * 60)
            print("📊 KẾT QUẢ TEST:")
            print(f"Thành công: {success}")
            print(f"Username: {account_result.get('username', 'N/A')}")
            print(f"Password: {account_result.get('password', 'N/A')}")
            print(f"Full Name: {account_result.get('full_name', 'N/A')}")
            print(f"Thông báo: {message}")
            print("=" * 60)
            
            if success:
                print("🎉 ĐĂNG KÝ THÀNH CÔNG!")
                print("✅ Chrome đã mở và đăng ký thật trên 13win16.com")
                print("✅ Username và password đã được tạo thật")
                print("📸 Kiểm tra screenshots trong thư mục data/screenshots/")
            else:
                print("❌ ĐĂNG KÝ THẤT BẠI!")
                print("💡 Có thể do:")
                print("   - ChromeDriver không tương thích")
                print("   - Website thay đổi")
                print("   - Kết nối mạng")
                print("   - Captcha cần giải thủ công")
        else:
            print("👍 Đã hủy test Chrome thật")
        
        print("\n🎉 Test hoàn thành!")
        return True
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        print("💡 Kiểm tra:")
        print("   - pip install selenium webdriver-manager")
        print("   - Cấu trúc thư mục src/")
        return False
    except Exception as e:
        print(f"❌ Lỗi khác: {e}")
        return False

def main():
    """Hàm main"""
    print("🚀 Test Real Chrome Registration Bot")
    print("Tool sẽ mở Chrome thật và đăng ký trên 13win16.com")
    print()
    
    success = test_real_chrome()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TEST THÀNH CÔNG!")
        print("💡 Bây giờ có thể chạy:")
        print("   start_real_chrome.bat")
        print("   python ui/simple_ui.py")
    else:
        print("❌ TEST THẤT BẠI!")
        print("💡 Thử chạy:")
        print("   demo_mode.bat (không cần Chrome)")
        print("   fix_chrome_137.bat (sửa ChromeDriver)")
    print("=" * 60)
    
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
