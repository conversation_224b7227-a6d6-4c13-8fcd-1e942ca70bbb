"""
Script test cho Auto Registration Tool
"""

from account_generator import AccountGenerator
from proxy_manager import ProxyManager
from registration_bot import RegistrationBot

def test_account_generator():
    """Test tạo thông tin tài khoản"""
    print("=== TEST ACCOUNT GENERATOR ===")

    generator = AccountGenerator()

    # Test tạo 3 tài khoản mẫu
    for i in range(3):
        print(f"\nTài khoản #{i+1}:")
        account = generator.generate_complete_account()

        print(f"Username: {account['username']}")
        print(f"Email: {account['email']}")
        print(f"Password: {account['password']}")
        print(f"Phone: {account['phone']}")
        print(f"Full Name: {account['full_name']}")

    print("\n✅ Account Generator hoạt động tốt!")

def test_proxy_manager():
    """Test quản lý proxy"""
    print("\n=== TEST PROXY MANAGER ===")

    proxy_manager = ProxyManager()

    # Test lấy proxy từ một nguồn
    print("Đang test lấy proxy từ nguồn...")
    test_url = "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt"
    proxies = proxy_manager.fetch_proxies_from_source(test_url)

    print(f"Lấy được {len(proxies)} proxy từ nguồn test")

    if proxies:
        print("Proxy mẫu:")
        for i, proxy in enumerate(proxies[:5]):  # Hiển thị 5 proxy đầu
            print(f"  {i+1}. {proxy}")

    print("\n✅ Proxy Manager hoạt động tốt!")

def test_form_analysis():
    """Test phân tích form đăng ký"""
    print("\n=== TEST FORM ANALYSIS ===")

    print("Đang phân tích form đăng ký...")
    print("⚠️  Lưu ý: Test này sẽ mở trình duyệt để phân tích form")

    confirm = input("Tiếp tục? (y/n): ").lower()
    if confirm != 'y':
        print("Đã bỏ qua test form analysis")
        return

    try:
        bot = RegistrationBot()
        selectors = bot.analyze_form()

        if selectors:
            print("✅ Phân tích form thành công!")
            print("Các trường tìm thấy:")
            for field, selector in selectors.items():
                print(f"  - {field}: {selector}")
        else:
            print("❌ Không thể phân tích form")

    except Exception as e:
        print(f"❌ Lỗi khi phân tích form: {e}")

def test_browser_creation():
    """Test tạo trình duyệt"""
    print("\n=== TEST BROWSER CREATION ===")

    print("Đang test tạo trình duyệt...")

    try:
        from browser_manager import BrowserManager

        # Test tạo browser không proxy
        print("Test 1: Tạo browser không proxy")
        browser = BrowserManager()
        if browser.create_browser():
            print("✅ Tạo browser thành công")

            # Test điều hướng
            if browser.navigate_to_url("https://httpbin.org/ip"):
                print("✅ Điều hướng thành công")

                # Lấy IP hiện tại
                import time
                time.sleep(3)
                page_source = browser.get_page_source()
                if "origin" in page_source:
                    print("✅ Lấy thông tin IP thành công")

            browser.close_browser()
        else:
            print("❌ Không thể tạo browser")

    except Exception as e:
        print(f"❌ Lỗi khi test browser: {e}")

def run_all_tests():
    """Chạy tất cả test"""
    print("🚀 BẮT ĐẦU TEST AUTO REGISTRATION TOOL")
    print("=" * 50)

    try:
        # Test 1: Account Generator
        test_account_generator()

        # Test 2: Proxy Manager
        test_proxy_manager()

        # Test 3: Browser Creation
        test_browser_creation()

        # Test 4: Form Analysis (optional)
        test_form_analysis()

        print("\n" + "=" * 50)
        print("🎉 TẤT CẢ TEST HOÀN THÀNH!")
        print("\nTool sẵn sàng sử dụng. Chạy lệnh: python main.py")

    except KeyboardInterrupt:
        print("\n⚠️  Test bị dừng bởi người dùng")
    except Exception as e:
        print(f"\n❌ Lỗi trong quá trình test: {e}")

if __name__ == "__main__":
    run_all_tests()
