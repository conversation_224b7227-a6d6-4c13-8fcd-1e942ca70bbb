@echo off
chcp 65001 >nul
title Auto Registration Tool - 13win16.com

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    AUTO REGISTRATION TOOL                    ║
echo ║                      13win16.com                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1] Cài đặt dependencies
echo [2] Khởi động Chrome debug mode
echo [3] Test tool
echo [4] Chạy tool chính
echo [5] Xem kết quả
echo [6] Thoát
echo.

set /p choice="Chọn tùy chọn (1-6): "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto chrome
if "%choice%"=="3" goto test
if "%choice%"=="4" goto run
if "%choice%"=="5" goto results
if "%choice%"=="6" goto exit

echo Lựa chọn không hợp lệ!
pause
goto start

:install
echo.
echo Đang cài đặt dependencies...
pip install -r requirements.txt
echo.
echo Cài đặt hoàn thành!
pause
goto start

:chrome
echo.
echo Đang khởi động Chrome debug mode...
call start_chrome_debug.bat
goto start

:test
echo.
echo Đang chạy test...
python test_tool.py
pause
goto start

:run
echo.
echo Đang khởi động tool...
python start_tool.py
pause
goto start

:results
echo.
echo Mở thư mục kết quả...
if exist successful_accounts.txt (
    echo Tài khoản thành công:
    type successful_accounts.txt
) else (
    echo Chưa có tài khoản nào được tạo thành công.
)
echo.
if exist failed_accounts.txt (
    echo Tài khoản thất bại:
    type failed_accounts.txt
)
pause
goto start

:exit
echo Tạm biệt!
exit

:start
cls
goto :eof
