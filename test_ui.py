"""
Test file để kiểm tra UI hoạt động
"""

import sys
from pathlib import Path

# Thêm đường dẫn
sys.path.append(str(Path(__file__).parent / "ui"))
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from main_window import MainWindow
    
    def test_ui():
        """Test giao diện UI"""
        print("🧪 Testing UI...")
        
        try:
            app = MainWindow()
            print("✅ UI khởi tạo thành công!")
            
            # Test một số chức năng cơ bản
            app.log_message("Test message", "INFO")
            app.log_message("Test success", "SUCCESS")
            app.log_message("Test error", "ERROR")
            
            print("✅ Logging hoạt động!")
            
            # Chạy UI
            print("🚀 Khởi động UI...")
            app.run()
            
        except Exception as e:
            print(f"❌ Lỗi UI: {e}")
            return False
        
        return True

    if __name__ == "__main__":
        test_ui()
        
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("Vui lòng đảm bảo cấu trúc file đúng!")
